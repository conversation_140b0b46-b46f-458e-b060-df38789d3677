import 'dart:io';

import 'package:flutter/material.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path/path.dart' as p;
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';

import '../services/document_scanner.dart';
import '../state/app_state.dart';

class FileTile extends StatelessWidget {
  final DocumentFile file;
  const FileTile({super.key, required this.file});

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(2)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(2)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(2)} GB';
  }

  @override
  Widget build(BuildContext context) {
    final color = Theme.of(context).colorScheme;
    final isFav = context.select<AppState, bool>((s) => s.favorites.isFavorite(file.path));

    return Card(
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.primary.withValues(alpha: 0.12),
          child: Icon(Icons.insert_drive_file, color: color.primary),
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                file.displayName,
                maxLines: 2, // Allow for slightly longer names
                overflow: TextOverflow.ellipsis,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(width: 8),
            SizedBox(
              width: 72, // Adjusted width for the size column
              child: Text(
                _formatBytes(file.sizeBytes),
                textAlign: TextAlign.end,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: color.onSurfaceVariant),
              ),
            ),
            IconButton(
              tooltip: isFav ? '取消收藏' : '收藏',
              icon: Icon(isFav ? Icons.star : Icons.star_border),
              onPressed: () async {
                final appState = context.read<AppState>();
                await appState.favorites.toggle(file.path);
                // Trigger UI refresh after toggling favorite
                appState.notifyListeners();
              },
            ),
            PopupMenuButton<String>(
              onSelected: (value) => _onMenu(context, value, file),
              itemBuilder: (context) => const [
                PopupMenuItem(value: 'open', child: Text('打开')),
                PopupMenuItem(value: 'rename', child: Text('改名')),
                PopupMenuItem(value: 'share', child: Text('分享')),
                PopupMenuItem(value: 'delete', child: Text('删除')),
              ],
            ),
          ],
        ),
        onTap: () => _onMenu(context, 'open', file),
      ),
    );
  }

  Future<void> _onMenu(BuildContext context, String action, DocumentFile f) async {
    switch (action) {
      case 'open':
        await OpenFilex.open(f.path);
        if (context.mounted) {
          context.read<AppState>().recents.add(f.path);
        }
        break;
      case 'rename':
        await _rename(context, f);
        break;
      case 'share':
        await Share.shareXFiles([XFile(f.path)], text: f.displayName);
        break;
      case 'delete':
        await _delete(context, f);
        break;
    }
  }

  Future<void> _rename(BuildContext context, DocumentFile f) async {
    final controller = TextEditingController(text: f.displayName);
    final newName = await showDialog<String>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('改名'),
          content: TextField(controller: controller, autofocus: true),
          actions: [
            TextButton(onPressed: () => Navigator.pop(context), child: const Text('取消')),
            FilledButton(onPressed: () => Navigator.pop(context, controller.text), child: const Text('确定')),
          ],
        );
      },
    );
    if (newName == null || newName.trim().isEmpty) return;
    final dir = p.dirname(f.path);
    final newPath = p.join(dir, newName);
    final fileObj = File(f.path);
    try {
      await fileObj.rename(newPath);
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('已改名')));
        await context.read<AppState>().refreshScan();
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('改名失败: $e')));
      }
    }
  }

  Future<void> _delete(BuildContext context, DocumentFile f) async {
    final ok = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除'),
        content: Text('确认删除文件：\n${f.displayName}?'),
        actions: [
          TextButton(onPressed: () => Navigator.pop(context, false), child: const Text('取消')),
          FilledButton(onPressed: () => Navigator.pop(context, true), child: const Text('删除')),
        ],
      ),
    );
    if (ok != true) return;
    try {
      final fileObj = File(f.path);
      if (await fileObj.exists()) {
        await fileObj.delete();
      }
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('已删除')));
        await context.read<AppState>().refreshScan();
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('删除失败: $e')));
      }
    }
  }
}
