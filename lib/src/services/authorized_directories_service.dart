import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'directory_picker_service.dart';

class AuthorizedDirectoriesService {
  static const String _key = 'authorized_directories';
  static const String _customNamesKey = 'authorized_directories_custom_names';
  static AuthorizedDirectoriesService? _instance;
  
  AuthorizedDirectoriesService._();
  
  static AuthorizedDirectoriesService get instance {
    _instance ??= AuthorizedDirectoriesService._();
    return _instance!;
  }

  Future<List<String>> getAuthorizedDirectories() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final directoriesJson = prefs.getString(_key);
      
      if (directoriesJson == null) {
        if (kDebugMode) {
          print('[AuthorizedDirectoriesService] No directories found in storage');
        }
        return [];
      }
      
      final List<dynamic> directoriesList = json.decode(directoriesJson);
      final directories = directoriesList.cast<String>();
      
      if (kDebugMode) {
        print('[AuthorizedDirectoriesService] Found ${directories.length} directories: $directories');
      }
      
      return directories;
    } catch (e) {
      if (kDebugMode) {
        print('[AuthorizedDirectoriesService] Error getting directories: $e');
      }
      return [];
    }
  }

  Future<bool> addDirectory(String directoryPath) async {
    try {
      if (kDebugMode) {
        print('[AuthorizedDirectoriesService] Adding directory: $directoryPath');
      }
      
      final directories = await getAuthorizedDirectories();
      
      // Check if directory is already authorized
      if (directories.contains(directoryPath)) {
        if (kDebugMode) {
          print('[AuthorizedDirectoriesService] Directory already authorized');
        }
        return true;
      }
      
      directories.add(directoryPath);
      final success = await _saveDirectories(directories);
      
      if (kDebugMode) {
        print('[AuthorizedDirectoriesService] Save result: $success');
      }
      
      return success;
    } catch (e) {
      if (kDebugMode) {
        print('[AuthorizedDirectoriesService] Error adding directory: $e');
      }
      return false;
    }
  }

  Future<bool> removeDirectory(String directoryPath) async {
    try {
      final directories = await getAuthorizedDirectories();
      final didRemove = directories.remove(directoryPath);
      
      if (didRemove) {
        if (kDebugMode) {
          print('[AuthorizedDirectoriesService] Removing directory: $directoryPath');
          print('[AuthorizedDirectoriesService] Remaining directories after removal: ${directories.length}');
        }
        
        // If this is the last directory, clear all bookmarks to ensure clean state
        if (directories.isEmpty) {
          if (kDebugMode) {
            print('[AuthorizedDirectoriesService] Last directory removed, clearing all bookmarks');
          }
          await DirectoryPickerService.clearAllBookmarks();
        } else {
          // Just revoke this specific directory
          await DirectoryPickerService.revokeAuthorization(directoryPath);
        }
        
        // Save the updated list back to prefs
        return await _saveDirectories(directories);
      } else {
        // Path was not in the list, nothing to do.
        return true;
      }
    } catch (e) {
      if (kDebugMode) {
        print('[AuthorizedDirectoriesService] Error removing directory: $e');
      }
      return false;
    }
  }

  Future<bool> _saveDirectories(List<String> directories) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final directoriesJson = json.encode(directories);
      return await prefs.setString(_key, directoriesJson);
    } catch (e) {
      if (kDebugMode) {
        print('[AuthorizedDirectoriesService] Error saving directories: $e');
      }
      return false;
    }
  }

  Future<void> clearAllDirectories() async {
    try {
      // First get all current directories to revoke their native bookmarks
      final directories = await getAuthorizedDirectories();
      if (kDebugMode) {
        print('[AuthorizedDirectoriesService] Clearing ${directories.length} directories');
      }
      
      // Clear all iOS native bookmarks
      await DirectoryPickerService.clearAllBookmarks();
      
      // Then clear the shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_key);
      await prefs.remove(_customNamesKey);
      
      if (kDebugMode) {
        print('[AuthorizedDirectoriesService] Cleared all directories from SharedPreferences');
      }
    } catch (e) {
      if (kDebugMode) {
        print('[AuthorizedDirectoriesService] Error clearing directories: $e');
      }
    }
  }

  Future<Map<String, String>> getCustomNames() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final customNamesJson = prefs.getString(_customNamesKey);
      
      if (customNamesJson == null) {
        return {};
      }
      
      final Map<String, dynamic> customNamesMap = json.decode(customNamesJson);
      return customNamesMap.cast<String, String>();
    } catch (e) {
      if (kDebugMode) {
        print('[AuthorizedDirectoriesService] Error getting custom names: $e');
      }
      return {};
    }
  }

  Future<bool> setCustomName(String directoryPath, String customName) async {
    try {
      final customNames = await getCustomNames();
      if (customName.trim().isEmpty) {
        customNames.remove(directoryPath);
      } else {
        customNames[directoryPath] = customName.trim();
      }
      
      final prefs = await SharedPreferences.getInstance();
      final customNamesJson = json.encode(customNames);
      return await prefs.setString(_customNamesKey, customNamesJson);
    } catch (e) {
      if (kDebugMode) {
        print('[AuthorizedDirectoriesService] Error setting custom name: $e');
      }
      return false;
    }
  }

  Future<String?> getCustomName(String directoryPath) async {
    try {
      final customNames = await getCustomNames();
      return customNames[directoryPath];
    } catch (e) {
      if (kDebugMode) {
        print('[AuthorizedDirectoriesService] Error getting custom name: $e');
      }
      return null;
    }
  }
}