import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as p;
import 'package:permission_handler/permission_handler.dart';

class DocumentFile {
  final String path;
  final String displayName;
  final int sizeBytes;
  final DateTime modified;

  const DocumentFile({
    required this.path,
    required this.displayName,
    required this.sizeBytes,
    required this.modified,
  });
}

class DocumentScanner {
  bool _initialized = false;

  List<DocumentFile> pdfs = [];
  List<DocumentFile> words = [];
  List<DocumentFile> excels = [];
  List<DocumentFile> ppts = [];

  Future<void> initialize() async {
    if (_initialized) return;
    // Request storage permissions on Android; on iOS we will use Files picker on demand
    if (Platform.isAndroid) {
      // Try granular first; if denied, try manageExternalStorage as fallback
      final status = await Permission.storage.request();
      if (!status.isGranted) {
        await Permission.manageExternalStorage.request();
      }
    }
    _initialized = true;
  }

  Future<void> scan() async {
    if (!Platform.isAndroid) {
      // On iOS, files are added manually by the user through the file picker.
      // We clear the lists to ensure no stale data from other platforms/sessions.
      pdfs = [];
      words = [];
      excels = [];
      ppts = [];
      return;
    }

    // Android-specific scanning logic remains the same.
    if (kDebugMode) {
      print('[DocumentScanner] Starting Android scan...');
    }
    final List<DocumentFile> foundPdfs = [];
    final List<DocumentFile> foundWords = [];
    final List<DocumentFile> foundExcels = [];
    final List<DocumentFile> foundPpts = [];

    // Common public folders to scan
    final List<String> roots = <String>[
      '/storage/emulated/0/Download',
      '/storage/emulated/0/Documents',
      '/storage/emulated/0/DCIM',
      '/storage/emulated/0/Pictures',
      '/storage/emulated/0/WhatsApp/Media/WhatsApp Documents',
      '/storage/emulated/0/Android/media',
    ];

    final Set<String> visited = <String>{};
    int scannedFiles = 0;
    const int maxFiles = 20000; // safety cap

    for (final root in roots) {
      await _scanPath(Directory(root), onFile: (entity) async {
        if (scannedFiles++ > maxFiles) return false;
        final path = entity.path;
        final lower = path.toLowerCase();
        final stat = await entity.stat();
        final doc = DocumentFile(
          path: path,
          displayName: p.basename(path),
          sizeBytes: stat.size,
          modified: stat.modified,
        );
        if (lower.endsWith('.pdf')) {
          foundPdfs.add(doc);
        } else if (lower.endsWith('.doc') || lower.endsWith('.docx')) {
          foundWords.add(doc);
        } else if (lower.endsWith('.xls') || lower.endsWith('.xlsx')) {
          foundExcels.add(doc);
        } else if (lower.endsWith('.ppt') || lower.endsWith('.pptx')) {
          foundPpts.add(doc);
        }
        return true;
      }, visited: visited);
    }

    pdfs = foundPdfs;
    words = foundWords;
    excels = foundExcels;
    ppts = foundPpts;
    if (kDebugMode) {
      print('[DocumentScanner] Android scan complete. Found ${pdfs.length} PDFs, ${words.length} Words, ${excels.length} Excels, ${ppts.length} PPTs.');
    }
  }

  void clearAll() {
    final oldCounts = 'PDFs: ${pdfs.length}, Words: ${words.length}, Excels: ${excels.length}, PPTs: ${ppts.length}';
    pdfs = [];
    words = [];
    excels = [];
    ppts = [];
    if (kDebugMode) {
      print('[DocumentScanner] Cleared all document lists. Previous counts - $oldCounts');
    }
  }

  // On iOS, we add files directly rather than scanning directories.
  void addFiles(List<DocumentFile> files, {bool replace = false}) {
    if (kDebugMode) {
      print('[DocumentScanner] addFiles called with ${files.length} files, replace: $replace');
    }
    
    if (replace) {
      clearAll();
    }
    
    final oldCounts = 'PDFs: ${pdfs.length}, Words: ${words.length}, Excels: ${excels.length}, PPTs: ${ppts.length}';
    
    for (final file in files) {
      final lower = file.path.toLowerCase();
      if (lower.endsWith('.pdf')) {
        // Avoid duplicates
        if (!pdfs.any((e) => e.path == file.path)) {
          pdfs.add(file);
          if (kDebugMode) {
            print('[DocumentScanner] Added PDF: ${file.displayName}');
          }
        }
      } else if (lower.endsWith('.doc') || lower.endsWith('.docx')) {
        if (!words.any((e) => e.path == file.path)) {
          words.add(file);
          if (kDebugMode) {
            print('[DocumentScanner] Added Word: ${file.displayName}');
          }
        }
      } else if (lower.endsWith('.xls') || lower.endsWith('.xlsx')) {
        if (!excels.any((e) => e.path == file.path)) {
          excels.add(file);
          if (kDebugMode) {
            print('[DocumentScanner] Added Excel: ${file.displayName}');
          }
        }
      } else if (lower.endsWith('.ppt') || lower.endsWith('.pptx')) {
        if (!ppts.any((e) => e.path == file.path)) {
          ppts.add(file);
          if (kDebugMode) {
            print('[DocumentScanner] Added PPT: ${file.displayName}');
          }
        }
      }
    }
    if (kDebugMode) {
      print('[DocumentScanner] Before: $oldCounts');
      print('[DocumentScanner] After adding ${files.length} files: PDFs: ${pdfs.length}, Words: ${words.length}, Excels: ${excels.length}, PPTs: ${ppts.length}');
    }
  }

  // Private helper for Android scanning.
  Future<void> _scanPath(
    Directory dir, {
    required Future<bool> Function(File entity) onFile,
    required Set<String> visited,
  }) async {
    if (!await dir.exists()) return;
    final path = dir.path;
    if (visited.contains(path)) return;
    visited.add(path);
    try {
      await for (final entity in dir.list(recursive: true, followLinks: false)) {
        if (entity is File) {
          final ok = await onFile(entity);
          if (!ok) return;
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('[DocumentScanner] Scan error at $path: $e');
      }
    }
  }

  
}
