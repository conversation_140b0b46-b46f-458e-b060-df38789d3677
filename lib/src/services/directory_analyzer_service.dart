import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:flutter/foundation.dart';
import 'directory_picker_service.dart' as ios_picker;

class DirectoryInfo {
  final String appName;
  final int fileCount;
  final String sizeFormatted;
  final int sizeInBytes;

  DirectoryInfo({
    required this.appName,
    required this.fileCount,
    required this.sizeFormatted,
    required this.sizeInBytes,
  });
}

class DirectoryAnalyzerService {
  static DirectoryAnalyzerService? _instance;
  
  DirectoryAnalyzerService._();
  
  static DirectoryAnalyzerService get instance {
    _instance ??= DirectoryAnalyzerService._();
    return _instance!;
  }


  Future<DirectoryInfo> analyzeDirectory(String directoryPath) async {
    try {
      if (kDebugMode) {
        print('[DirectoryAnalyzer] Analyzing directory: $directoryPath');
      }
      
      // On iOS/Android, first try platform channel methods (bookmarks/SAF).
      if (defaultTargetPlatform == TargetPlatform.iOS || defaultTargetPlatform == TargetPlatform.android) {
        try {
          final stats = await ios_picker.DirectoryPickerService.getDirectoryStats(directoryPath);
          if (stats != null) {
            return DirectoryInfo(
              appName: await _getAppNameFromPath(directoryPath),
              fileCount: stats.fileCount,
              sizeFormatted: _formatBytes(stats.totalBytes),
              sizeInBytes: stats.totalBytes,
            );
          }
        } catch (_) {
          // Fallback to Dart traversal below
        }
      }

      // Prefer analyzing the Documents subfolder for iOS app containers to avoid
      // permission errors when traversing Library/tmp within the container.
      String basePath = directoryPath;
      if (directoryPath.contains('/var/mobile/Containers/Data/Application/')) {
        final docsCandidate = path.join(directoryPath, 'Documents');
        final docsDir = Directory(docsCandidate);
        if (await docsDir.exists()) {
          basePath = docsCandidate;
        }
      }

      final directory = Directory(basePath);
      
      if (!await directory.exists()) {
        if (kDebugMode) {
          print('[DirectoryAnalyzer] Directory does not exist: $basePath');
        }
        return DirectoryInfo(
          appName: await _getAppNameFromPath(directoryPath),
          fileCount: 0,
          sizeFormatted: '0 B',
          sizeInBytes: 0,
        );
      }

      int fileCount = 0;
      int totalSize = 0;

      // Traverse the directory tree, but ignore unreadable nodes so one failure
      // doesn’t cancel the whole walk and force us to show 0 files / 0 B.
      final stream = directory
          .list(recursive: true, followLinks: false)
          .handleError((e) {
        if (kDebugMode) {
          print('[DirectoryAnalyzer] Skipping unreadable entry: $e');
        }
      }, test: (e) => e is FileSystemException);

      await for (final entity in stream) {
        if (entity is File) {
          fileCount++;
          try {
            final stat = await entity.stat();
            totalSize += stat.size;
          } catch (e) {
            if (kDebugMode) {
              print('[DirectoryAnalyzer] Error getting file size for ${entity.path}: $e');
            }
          }
        }
      }

      return DirectoryInfo(
        appName: await _getAppNameFromPath(directoryPath),
        fileCount: fileCount,
        sizeFormatted: _formatBytes(totalSize),
        sizeInBytes: totalSize,
      );
    } catch (e) {
      if (kDebugMode) {
        print('[DirectoryAnalyzer] Error analyzing directory $directoryPath: $e');
      }
      
      return DirectoryInfo(
        appName: await _getAppNameFromPath(directoryPath),
        fileCount: 0,
        sizeFormatted: '0 B',
        sizeInBytes: 0,
      );
    }
  }

  Future<String> _getAppNameFromPath(String directoryPath) async {
    try {
      if (kDebugMode) {
        print('[DirectoryAnalyzer] Analyzing path: $directoryPath');
      }
      
      // Handle common iOS/iPadOS paths
      if (directoryPath.contains('/var/mobile/Containers/')) {
        final segments = directoryPath.split('/');
        
        // First, try to read container metadata for accurate app identification
        final appNameFromMetadata = await _tryReadContainerMetadata(directoryPath);
        if (appNameFromMetadata.isNotEmpty) {
          return appNameFromMetadata;
        }
        
        // Look for Data/Application container patterns
        for (int i = 0; i < segments.length; i++) {
          if (segments[i] == 'Data' && i + 1 < segments.length && segments[i + 1] == 'Application') {
            // Check if this is a LocalSend path by examining the UUID and Documents structure
            if (i + 2 < segments.length) {
              final uuidSegment = segments[i + 2];
              // The path pattern suggests this might be LocalSend if it ends with Documents
              if (segments.length > i + 3 && segments[i + 3] == 'Documents') {
                // Try to identify LocalSend by checking for characteristic files or patterns
                final detectedApp = await _identifyAppFromContainerStructure(directoryPath, uuidSegment);
                if (detectedApp.isNotEmpty) {
                  return detectedApp;
                }
              }
            }
          }
          
          // Look for Application container patterns
          if (segments[i] == 'Application' && i + 2 < segments.length) {
            final nextSegment = i + 2 < segments.length ? segments[i + 2] : '';
            
            // Try to find app identifier in the path structure
            if (nextSegment.contains('.app')) {
              return _prettifyAppName(nextSegment.replaceAll('.app', ''));
            }
          }
        }
      }
      
      // Handle iCloud Drive paths
      if (directoryPath.contains('Mobile Documents') || directoryPath.contains('iCloud')) {
        if (directoryPath.contains('iCloud~')) {
          final icloudMatch = RegExp(r'iCloud~([^/]+)').firstMatch(directoryPath);
          if (icloudMatch != null) {
            final appId = icloudMatch.group(1) ?? '';
            if (appId.isNotEmpty) {
              return _prettifyAppName(appId);
            }
          }
        }
        
        // Check for iCloud Drive specific patterns
        if (directoryPath.contains('com~apple~CloudDocs')) {
          return 'iCloud Drive';
        }
        
        return 'iCloud Drive';
      }
      
      // Handle common app names in paths - be more specific
      final pathLower = directoryPath.toLowerCase();
      
      if (pathLower.contains('/localsend/') || pathLower.endsWith('localsend')) {
        return 'LocalSend';
      }
      
      if (pathLower.contains('/downloads/') || pathLower.endsWith('downloads')) {
        return 'Downloads';
      }
      
      if (pathLower.contains('/dropbox/') || pathLower.endsWith('dropbox')) {
        return 'Dropbox';
      }
      
      if (pathLower.contains('/onedrive/') || pathLower.endsWith('onedrive')) {
        return 'OneDrive';
      }
      
      // Handle bundle ID patterns in path
      final bundleIdMatch = RegExp(r'com\.([^\.]+)\.([^/]+)').firstMatch(directoryPath);
      if (bundleIdMatch != null) {
        final company = bundleIdMatch.group(1) ?? '';
        final appName = bundleIdMatch.group(2) ?? '';
        if (appName.isNotEmpty && appName != 'app') {
          return _prettifyAppName(appName);
        }
        if (company.isNotEmpty) {
          return _prettifyAppName(company);
        }
      }
      
      // Fallback: use the most meaningful directory name
      final segments = directoryPath.split('/').where((s) => s.isNotEmpty).toList();
      if (segments.isNotEmpty) {
        // Try from the end, skipping common generic names
        for (int i = segments.length - 1; i >= 0; i--) {
          final segment = segments[i];
          if (_isValidAppName(segment)) {
            return _prettifyAppName(segment);
          }
        }
      }
      
      return 'Unknown App';
    } catch (e) {
      if (kDebugMode) {
        print('[DirectoryAnalyzer] Error extracting app name from $directoryPath: $e');
      }
      return 'Unknown App';
    }
  }

  bool _isValidAppName(String segment) {
    // Skip common generic directory names
    final genericNames = {
      'documents', 'data', 'library', 'caches', 'tmp', 'private', 'var', 
      'mobile', 'containers', 'application', 'shared', 'group'
    };
    
    final segmentLower = segment.toLowerCase();
    
    // Skip if it's a generic name
    if (genericNames.contains(segmentLower)) {
      return false;
    }
    
    // Skip if it looks like a UUID (long hex string)
    if (segment.length > 30 && RegExp(r'^[0-9A-F-]+$', caseSensitive: false).hasMatch(segment)) {
      return false;
    }
    
    // Skip very short names (less than 3 characters)
    if (segment.length < 3) {
      return false;
    }
    
    return true;
  }

  Future<String> _tryReadContainerMetadata(String directoryPath) async {
    try {
      // Try to find the container root and read metadata
      final segments = directoryPath.split('/');
      int applicationIndex = -1;
      
      for (int i = 0; i < segments.length; i++) {
        if (segments[i] == 'Application' && i > 0 && segments[i - 1] == 'Data') {
          applicationIndex = i;
          break;
        }
      }
      
      if (applicationIndex > 0 && applicationIndex + 1 < segments.length) {
        final containerRoot = segments.sublist(0, applicationIndex + 2).join('/');
        final metadataPath = '$containerRoot/.com.apple.mobile_container_manager.metadata.plist';
        
        if (kDebugMode) {
          print('[DirectoryAnalyzer] Trying to read metadata: $metadataPath');
        }
        
        final metadataFile = File(metadataPath);
        if (await metadataFile.exists()) {
          final content = await metadataFile.readAsString();
          
          // Look for bundle identifier in the plist content
          final bundleIdMatch = RegExp(r'<key>MCMMetadataIdentifier</key>\s*<string>([^<]+)</string>').firstMatch(content);
          if (bundleIdMatch != null) {
            final bundleId = bundleIdMatch.group(1) ?? '';
            if (bundleId.isNotEmpty) {
              if (kDebugMode) {
                print('[DirectoryAnalyzer] Found bundle ID: $bundleId');
              }
              return _getBundleDisplayName(bundleId);
            }
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('[DirectoryAnalyzer] Error reading container metadata: $e');
      }
    }
    
    return '';
  }

  Future<String> _identifyAppFromContainerStructure(String directoryPath, String uuid) async {
    try {
      final directory = Directory(directoryPath);
      if (!await directory.exists()) {
        return '';
      }
      
      // Check for LocalSend-specific patterns
      final entities = await directory.list(recursive: false).toList();
      
      // LocalSend typically creates specific file patterns or has characteristic files
      for (final entity in entities) {
        final fileName = path.basename(entity.path).toLowerCase();
        
        // Look for LocalSend characteristic files or patterns
        if (fileName.contains('localsend') || 
            fileName.contains('received') ||
            fileName.contains('shared')) {
          return 'LocalSend';
        }
      }
      
      // Check parent directories for more clues
      final parentSegments = directoryPath.split('/');
      if (parentSegments.length >= 3) {
        final containerPath = parentSegments.sublist(0, parentSegments.length - 1).join('/');
        final containerDir = Directory(containerPath);
        
        if (await containerDir.exists()) {
          await for (final entity in containerDir.list(recursive: false)) {
            final fileName = path.basename(entity.path).toLowerCase();
            if (fileName.contains('localsend')) {
              return 'LocalSend';
            }
          }
        }
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('[DirectoryAnalyzer] Error identifying app from structure: $e');
      }
    }
    
    return '';
  }

  String _getBundleDisplayName(String bundleId) {
    // Map known bundle IDs to friendly names
    final knownApps = {
      'org.localsend.localsend_app': 'LocalSend',
      'com.apple.DocumentsApp': 'Files',
      'com.apple.CloudDocs': 'iCloud Drive',
      'com.getdropbox.Dropbox': 'Dropbox',
      'com.microsoft.skydrive': 'OneDrive',
    };
    
    if (knownApps.containsKey(bundleId)) {
      return knownApps[bundleId]!;
    }
    
    // Use the prettify method for unknown bundle IDs
    return _prettifyAppName(bundleId);
  }

  String _prettifyAppName(String name) {
    if (name.isEmpty) {
      return 'Unknown App';
    }
    
    // Handle special known cases first
    if (name.toLowerCase() == 'localsend') {
      return 'LocalSend';
    }
    
    // Remove common prefixes/suffixes
    name = name.replaceAll(RegExp(r'^com\.'), '');
    name = name.replaceAll(RegExp(r'^org\.'), '');
    name = name.replaceAll(RegExp(r'^net\.'), '');
    name = name.replaceAll(RegExp(r'\.app$'), '');
    
    // Handle tilde separated iCloud identifiers
    if (name.contains('~')) {
      final parts = name.split('~');
      if (parts.length >= 2) {
        // Usually the last part is the app name
        name = parts.last;
      }
    }
    
    // Handle bundle ID format (com.company.appname)
    if (name.contains('.')) {
      final parts = name.split('.');
      if (parts.length >= 2) {
        // Take the last meaningful part
        name = parts.where((part) => part.isNotEmpty && part != 'app').last;
      }
    }
    
    // Avoid generic words that might result in "Application"
    final genericWords = {'app', 'application', 'client', 'viewer', 'editor'};
    if (genericWords.contains(name.toLowerCase())) {
      return 'Unknown App';
    }
    
    // Convert camelCase or snake_case to readable format
    name = name.replaceAllMapped(RegExp(r'([a-z])([A-Z])'), (match) {
      return '${match.group(1)} ${match.group(2)}';
    });
    name = name.replaceAll('_', ' ');
    name = name.replaceAll('-', ' ');
    
    // Clean up multiple spaces
    name = name.replaceAll(RegExp(r'\s+'), ' ').trim();
    
    // Capitalize words properly
    final words = name.split(' ');
    final capitalizedWords = words.map((word) {
      if (word.isEmpty) return word;
      
      // Handle special cases (all caps abbreviations)
      if (word.length <= 3 && word.toUpperCase() == word) {
        return word.toUpperCase();
      }
      
      return '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}';
    }).toList();
    
    final result = capitalizedWords.join(' ');
    
    if (kDebugMode) {
      print('[DirectoryAnalyzer] Prettified "$name" to "$result"');
    }
    
    return result.isNotEmpty ? result : 'Unknown App';
  }

  String _formatBytes(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
}
