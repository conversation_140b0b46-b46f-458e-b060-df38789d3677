import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

class DirectoryPickerService {
  static const MethodChannel _channel = MethodChannel('directory_picker');

  static Future<DirectoryPickResult?> pickDirectory() async {
    try {
      if (!(Platform.isIOS || Platform.isAndroid)) {
        throw UnsupportedError('Directory picker is only supported on iOS/Android');
      }

      final result = await _channel.invokeMethod('pickDirectory');
      if (result != null && result is Map) {
        final resultMap = Map<String, dynamic>.from(result);
        return DirectoryPickResult(
          path: resultMap['path'] as String,
          name: resultMap['name'] as String,
          success: resultMap['success'] as bool,
        );
      }
      return null;
    } on PlatformException catch (e) {
      if (kDebugMode) {
        print('Failed to pick directory: ${e.message}');
      }
      return null;
    }
  }

  static Future<List<FileInfo>?> getDirectoryFiles() async {
    try {
      if (!(Platform.isIOS || Platform.isAndroid)) {
        throw UnsupportedError('Directory picker is only supported on iOS/Android');
      }

      final result = await _channel.invokeMethod('getDirectoryFiles');
      if (result != null && result is List) {
        return result.map((fileData) {
          final file = Map<String, dynamic>.from(fileData as Map);
          return FileInfo(
            name: file['name'] as String,
            path: file['path'] as String,
            size: file['size'] as int,
            isDirectory: file['isDirectory'] as bool,
            modificationDate: file['modificationDate'] != null
                ? DateTime.fromMillisecondsSinceEpoch(file['modificationDate'] as int)
                : null,
          );
        }).toList();
      }
      return null;
    } on PlatformException catch (e) {
      if (kDebugMode) {
        print('Failed to get directory files: ${e.message}');
      }
      if (e.code == 'NO_BOOKMARK' || e.code == 'STALE_BOOKMARK') {
        return [];
      }
      return null;
    } on MissingPluginException catch (e) {
      if (kDebugMode) {
        print('Directory picker plugin not properly initialized: $e');
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Unexpected error getting directory files: $e');
      }
      return null;
    }
  }

  static Future<List<FileInfo>?> getDirectoryFilesForPath(String path) async {
    try {
      if (!(Platform.isIOS || Platform.isAndroid)) {
        throw UnsupportedError('Directory picker is only supported on iOS/Android');
      }
      final result = await _channel.invokeMethod('getDirectoryFilesForPath', {
        'path': path,
      });
      if (result != null && result is List) {
        return result.map((fileData) {
          final file = Map<String, dynamic>.from(fileData as Map);
          return FileInfo(
            name: file['name'] as String,
            path: file['path'] as String,
            size: file['size'] as int,
            isDirectory: file['isDirectory'] as bool,
            modificationDate: file['modificationDate'] != null
                ? DateTime.fromMillisecondsSinceEpoch(file['modificationDate'] as int)
                : null,
          );
        }).toList();
      }
      return null;
    } on PlatformException catch (e) {
      if (kDebugMode) {
        print('Failed to get directory files for path: ${e.code} ${e.message}');
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Unexpected error getting directory files for path: $e');
      }
      return null;
    }
  }

  static Future<DirectoryStats?> getDirectoryStats(String path) async {
    try {
      if (!(Platform.isIOS || Platform.isAndroid)) {
        throw UnsupportedError('Directory stats via channel is only supported on iOS/Android');
      }
      final result = await _channel.invokeMethod('getDirectoryStats', {
        'path': path,
      });
      if (result != null && result is Map) {
        final map = Map<String, dynamic>.from(result);
        return DirectoryStats(
          fileCount: (map['fileCount'] as num).toInt(),
          totalBytes: (map['totalBytes'] as num).toInt(),
        );
      }
      return null;
    } on PlatformException catch (e) {
      if (kDebugMode) {
        print('Failed to get directory stats: ${e.code} ${e.message}');
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Unexpected error getting directory stats: $e');
      }
      return null;
    }
  }

  static Future<void> revokeAuthorization(String path) async {
    try {
      if (!Platform.isIOS) return; // This is an iOS-specific feature
      await _channel.invokeMethod('revokeAuthorization', {
        'path': path,
      });
    } on PlatformException catch (e) {
      if (kDebugMode) {
        print('Failed to revoke authorization: ${e.message}');
      }
    }
  }

  static Future<void> clearAllBookmarks() async {
    try {
      if (!Platform.isIOS) return; // This is an iOS-specific feature
      await _channel.invokeMethod('clearAllBookmarks');
      if (kDebugMode) {
        print('[DirectoryPickerService] All iOS bookmarks cleared');
      }
    } on PlatformException catch (e) {
      if (kDebugMode) {
        print('Failed to clear all bookmarks: ${e.message}');
      }
    }
  }
}

class DirectoryPickResult {
  final String path;
  final String name;
  final bool success;

  DirectoryPickResult({
    required this.path,
    required this.name,
    required this.success,
  });
}

class FileInfo {
  final String name;
  final String path;
  final int size;
  final bool isDirectory;
  final DateTime? modificationDate;

  FileInfo({
    required this.name,
    required this.path,
    required this.size,
    required this.isDirectory,
    this.modificationDate,
  });

  String get extension {
    if (isDirectory) return '';
    final parts = name.split('.');
    return parts.length > 1 ? parts.last.toLowerCase() : '';
  }

  String get formattedSize {
    if (size < 1024) return '$size B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)} KB';
    if (size < 1024 * 1024 * 1024) return '${(size / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  bool get isPDF => extension == 'pdf';
  bool get isImage => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp'].contains(extension);
  bool get isDocument => ['doc', 'docx', 'txt', 'rtf', 'pages'].contains(extension);
  bool get isSpreadsheet => ['xls', 'xlsx', 'csv', 'numbers'].contains(extension);
  bool get isPresentation => ['ppt', 'pptx', 'key'].contains(extension);
}

class DirectoryStats {
  final int fileCount;
  final int totalBytes;

  DirectoryStats({required this.fileCount, required this.totalBytes});
}
