import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../l10n/app_localizations.dart';
import '../services/authorized_directories_service.dart';
import '../services/directory_analyzer_service.dart';
import '../services/directory_picker_service.dart';
import '../state/app_state.dart';

class AuthorizedDirectoriesScreen extends StatefulWidget {
  const AuthorizedDirectoriesScreen({super.key});

  @override
  State<AuthorizedDirectoriesScreen> createState() => _AuthorizedDirectoriesScreenState();
}

class _AuthorizedDirectoriesScreenState extends State<AuthorizedDirectoriesScreen> {
  final AuthorizedDirectoriesService _directoriesService = AuthorizedDirectoriesService.instance;
  final DirectoryAnalyzerService _analyzerService = DirectoryAnalyzerService.instance;
  List<String> _directories = [];
  Map<String, DirectoryInfo> _directoryInfos = {};
  Map<String, String> _customNames = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDirectories();
  }

  Future<void> _loadDirectories() async {
    if (kDebugMode) {
      print('[AuthorizedDirectoriesScreen] Loading directories');
    }
    
    final directories = await _directoriesService.getAuthorizedDirectories();
    final customNames = await _directoriesService.getCustomNames();
    
    // Analyze each directory
    final Map<String, DirectoryInfo> infos = {};
    for (final directory in directories) {
      try {
        if (kDebugMode) {
          print('[AuthorizedDirectoriesScreen] Analyzing directory: $directory');
        }
        infos[directory] = await _analyzerService.analyzeDirectory(directory);
        if (kDebugMode) {
          final info = infos[directory]!;
          print('[AuthorizedDirectoriesScreen] Analysis result - App: ${info.appName}, Files: ${info.fileCount}, Size: ${info.sizeFormatted}');
        }
      } catch (e) {
        if (kDebugMode) {
          print('[AuthorizedDirectoriesScreen] Error analyzing directory $directory: $e');
        }
        // Fallback info
        infos[directory] = DirectoryInfo(
          appName: 'Unknown App',
          fileCount: 0,
          sizeFormatted: '0 B',
          sizeInBytes: 0,
        );
      }
    }
    
    if (mounted) {
      setState(() {
        _directories = directories;
        _directoryInfos = infos;
        _customNames = customNames;
        _isLoading = false;
      });
    }
  }

  Future<void> _addDirectory() async {
    final l10n = AppLocalizations.of(context)!;
    
    try {
      String? resultPath;
      if (Platform.isIOS || Platform.isAndroid) {
        if (kDebugMode) {
          print('[AuthorizedDirectoriesScreen] Using platform DirectoryPickerService');
        }
        final pick = await DirectoryPickerService.pickDirectory();
        if (pick != null && pick.success) {
          resultPath = pick.path;
        }
      } else {
        final result = await FilePicker.platform.getDirectoryPath();
        resultPath = result;
      }
      
      if (resultPath != null) {
        if (kDebugMode) {
          print('[AuthorizedDirectoriesScreen] Selected directory: $resultPath');
        }
        
        final success = await _directoriesService.addDirectory(resultPath);
        
        if (success) {
          // Get the latest directory info
          final info = await _analyzerService.analyzeDirectory(resultPath);
          
          // If the app name couldn't be determined, generate a default name
          // and trigger the edit dialog.
          if (info.appName == 'Unknown App') {
            // Find the next available "App" number
            int nextAppNumber = 1;
            final allNames = _directories.map((d) {
              return _customNames[d] ?? _directoryInfos[d]?.appName;
            }).toList();
            
            while (allNames.contains(l10n.defaultAppName(nextAppNumber))) {
              nextAppNumber++;
            }
            
            final defaultName = l10n.defaultAppName(nextAppNumber);
            
            // Save the default name and then immediately open the editor
            await _directoriesService.setCustomName(resultPath, defaultName);
            await _loadDirectories(); // Reload to get the new state
            
            // Refresh AppState to include files from the new directory
            if (mounted) {
              if (kDebugMode) {
                print('[AuthorizedDirectoriesScreen] New directory added (auto-edit), refreshing AppState...');
              }
              final appState = context.read<AppState>();
              await appState.refreshScan();
              if (kDebugMode) {
                print('[AuthorizedDirectoriesScreen] AppState refresh completed for auto-edit directory');
              }
              
              // Use a short delay to ensure the UI has updated before showing the dialog
              await Future.delayed(const Duration(milliseconds: 200));
              _editDirectoryName(resultPath, isAutoEdit: true);
            }
          } else {
            // If the name was found, just show a confirmation and reload
            await _loadDirectories();
            
            // Refresh AppState to include files from the new directory
            if (mounted) {
              if (kDebugMode) {
                print('[AuthorizedDirectoriesScreen] Directory added, refreshing AppState...');
              }
              final appState = context.read<AppState>();
              await appState.refreshScan();
              if (kDebugMode) {
                print('[AuthorizedDirectoriesScreen] AppState refresh completed after adding directory');
              }
              
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(l10n.directoryAdded)),
              );
            }
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(l10n.cannotAddDirectory)),
            );
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('[AuthorizedDirectoriesScreen] Error adding directory: $e');
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(l10n.cannotAddDirectory)),
        );
      }
    }
  }

  Future<void> _removeDirectory(String directoryPath) async {
    final l10n = AppLocalizations.of(context)!;
    
    final success = await _directoriesService.removeDirectory(directoryPath);
    
    if (success) {
      await _loadDirectories();
      
      // Refresh AppState to remove files from the deleted directory
      if (mounted) {
        if (kDebugMode) {
          print('[AuthorizedDirectoriesScreen] Directory removed, refreshing AppState...');
        }
        final appState = context.read<AppState>();
        await appState.refreshScan();
        if (kDebugMode) {
          print('[AuthorizedDirectoriesScreen] AppState refresh completed');
        }
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(l10n.directoryRemoved)),
        );
      }
    }
  }

  void _showRemoveDialog(String directoryPath) {
    final l10n = AppLocalizations.of(context)!;
    final info = _directoryInfos[directoryPath];
    
    // Use custom name if available, otherwise use detected app name
    final appName = _customNames[directoryPath] ?? info?.appName ?? 'Unknown App';
    final directoryName = _getFriendlyDirectoryName(directoryPath);
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(l10n.cancelAuthorization),
          content: Text(l10n.confirmCancelAuthorization(appName, directoryName)),
          actionsAlignment: MainAxisAlignment.end,
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                foregroundColor: Theme.of(context).colorScheme.onSurfaceVariant,
                elevation: 0,
              ),
              child: Text(l10n.noKeepIt),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _removeDirectory(directoryPath);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.error,
                foregroundColor: Theme.of(context).colorScheme.onError,
                elevation: 0,
              ),
              child: Text(l10n.yesRemoveFolder),
            ),
          ],
        );
      },
    );
  }

  Future<void> _editDirectoryName(String directoryPath, {bool isAutoEdit = false}) async {
    final l10n = AppLocalizations.of(context)!;
    final info = _directoryInfos[directoryPath];
    final currentName = _customNames[directoryPath] ?? info?.appName ?? 'Unknown App';
    
    final result = await showDialog<String>(
      context: context,
      barrierDismissible: !isAutoEdit, // Auto-edit mode requires explicit action
      builder: (BuildContext dialogContext) {
        return _EditNameDialog(
          initialName: currentName,
          l10n: l10n,
          isAutoEdit: isAutoEdit,
        );
      },
    );
    
    if (result != null && mounted) {
      final success = await _directoriesService.setCustomName(directoryPath, result);
      if (success && mounted) {
        await _loadDirectories();
      }
    }
  }

  String _getFriendlyDirectoryName(String directoryPath) {
    // Extract a user-friendly directory name from the path
    final segments = directoryPath.split('/').where((s) => s.isNotEmpty).toList();
    
    if (segments.isEmpty) {
      return 'Documents';
    }
    
    final lastSegment = segments.last;
    
    // Map technical names to friendly names
    final friendlyNames = {
      'Documents': 'Documents',
      'Downloads': 'Downloads',
      'Pictures': 'Pictures',
      'Movies': 'Videos',
      'Music': 'Music',
      'Desktop': 'Desktop',
      'Library': 'Library',
    };
    
    if (friendlyNames.containsKey(lastSegment)) {
      return friendlyNames[lastSegment]!;
    }
    
    // For container paths, use "Documents" as default
    if (directoryPath.contains('/var/mobile/Containers/')) {
      return 'Documents';
    }
    
    // For iCloud paths, try to extract meaningful name
    if (directoryPath.contains('iCloud')) {
      return 'iCloud Drive';
    }
    
    return lastSegment.isNotEmpty ? lastSegment : 'Documents';
  }

  Widget _buildDirectoryTile(String directoryPath) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;
    final info = _directoryInfos[directoryPath];
    
    if (info == null) {
      return const SizedBox.shrink();
    }
    
    // Use custom name if available, otherwise use detected app name
    final displayName = _customNames[directoryPath] ?? info.appName;
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: Icon(
          Icons.folder_outlined,
          color: theme.colorScheme.primary,
        ),
        title: Text(
          displayName,
          style: theme.textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          '${l10n.filesCount(info.fileCount)} • ${l10n.directorySize(info.sizeFormatted)}',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: Icon(
                Icons.edit_outlined,
                color: theme.colorScheme.primary,
              ),
              onPressed: () => _editDirectoryName(directoryPath),
              tooltip: l10n.editName,
            ),
            IconButton(
              icon: Icon(
                Icons.delete_outline,
                color: theme.colorScheme.error,
              ),
              onPressed: () => _showRemoveDialog(directoryPath),
              tooltip: l10n.removeDirectory,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;
    // Use a scrollable + constrained layout so the content
    // centers on tall screens and scrolls on short (landscape) ones.
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(32.0),
          child: ConstrainedBox(
            constraints: BoxConstraints(minHeight: constraints.maxHeight),
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.folder_off_outlined,
                    size: 80,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    l10n.noAuthorizedDirectories,
                    style: theme.textTheme.headlineSmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    l10n.noAuthorizedDirectoriesSubtitle,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),
                  FilledButton.icon(
                    onPressed: _addDirectory,
                    icon: const Icon(Icons.add),
                    label: Text(l10n.addDirectory),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(
          l10n.authorizedDirectories,
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        actions: Platform.isIOS
            ? [
                IconButton(
                  onPressed: _addDirectory,
                  icon: const Icon(Icons.add),
                  tooltip: l10n.addDirectory,
                ),
              ]
            : null,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _directories.isEmpty
              ? _buildEmptyState()
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header with app access info
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        l10n.appCanAccessDirectories(l10n.appName),
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                    ),
                    // Directory list
                    Expanded(
                      child: ListView.builder(
                        padding: const EdgeInsets.only(bottom: 8),
                        itemCount: _directories.length,
                        itemBuilder: (context, index) {
                          return _buildDirectoryTile(_directories[index]);
                        },
                      ),
                    ),
                  ],
                ),
      floatingActionButton: Platform.isAndroid && !_isLoading
          ? FloatingActionButton(
              onPressed: _addDirectory,
              tooltip: l10n.addDirectory,
              child: const Icon(Icons.add),
            )
          : null,
    );
  }
}

class _EditNameDialog extends StatefulWidget {
  final String initialName;
  final AppLocalizations l10n;
  final bool isAutoEdit;

  const _EditNameDialog({
    required this.initialName,
    required this.l10n,
    this.isAutoEdit = false,
  });

  @override
  State<_EditNameDialog> createState() => _EditNameDialogState();
}

class _EditNameDialogState extends State<_EditNameDialog> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialName);
    // Select all text for quick editing
    _controller.selection = TextSelection(baseOffset: 0, extentOffset: _controller.text.length);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 400,
          maxHeight: 300,
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title
              Text(
                widget.l10n.editName,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 16),
              
              // Description
              Text(
                widget.isAutoEdit 
                    ? widget.l10n.nameForNewFolder
                    : widget.l10n.enterCustomName,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 12),
              
              // TextField
              TextField(
                controller: _controller,
                decoration: InputDecoration(
                  labelText: widget.l10n.customName,
                  border: const OutlineInputBorder(),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 12,
                  ),
                ),
                autofocus: true,
                maxLines: 1,
                onSubmitted: (value) {
                  if (value.trim().isNotEmpty) {
                    Navigator.of(context).pop(value);
                  }
                },
              ),
              const SizedBox(height: 20),
              
              // Actions
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(widget.l10n.cancel),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () {
                      final text = _controller.text.trim();
                      if (text.isNotEmpty) {
                        Navigator.of(context).pop(text);
                      } else {
                        Navigator.of(context).pop();
                      }
                    },
                    child: Text(widget.l10n.save),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
