import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:provider/provider.dart';

import '../app_theme.dart';
import '../state/app_state.dart';
import '../../l10n/app_localizations.dart';
import 'crop_screen.dart';

enum OperationType {
  addImage,
  deleteImage,
  reorderImages,
  cropImage,
  takePicture,
}

class UndoOperation {
  final OperationType type;
  final List<File> previousImages;
  final Map<String, dynamic> metadata;
  final String description;

  UndoOperation({
    required this.type,
    required this.previousImages,
    required this.description,
    this.metadata = const {},
  });
}

class EditorScreen extends StatefulWidget {
  final bool fromCamera;
  final bool fromGallery;
  const EditorScreen({super.key, this.fromCamera = false, this.fromGallery = false});

  @override
  State<EditorScreen> createState() => _EditorScreenState();
}

class _EditorScreenState extends State<EditorScreen> {
  final ImagePicker _picker = ImagePicker();
  final List<File> _images = [];
  final List<UndoOperation> _undoStack = [];
  bool _saving = false;
  bool _hasUserActions = false; // Track if user has performed actual editing actions

  @override
  void initState() {
    super.initState();
    Future.microtask(() async {
      if (widget.fromCamera) {
        await _takePictureInitial();
      } else if (widget.fromGallery) {
        await _pickFromGalleryInitial();
      }
    });
  }

  void _pushUndoOperation(OperationType type, String description, {Map<String, dynamic>? metadata, bool isUserAction = true}) {
    _undoStack.add(UndoOperation(
      type: type,
      previousImages: List<File>.from(_images),
      description: description,
      metadata: metadata ?? {},
    ));
    
    // Mark that user has performed actions (not initial camera/gallery)
    if (isUserAction) {
      _hasUserActions = true;
    }
    
    // Limit undo stack size
    if (_undoStack.length > 20) {
      _undoStack.removeAt(0);
    }
  }

  String _getUndoDescription() {
    if (_undoStack.isEmpty) return '';
    final lastOp = _undoStack.last;
    return lastOp.description;
  }

  Future<bool> _onWillPop() async {
    if (!_hasUserActions || _saving) {
      return true;
    }

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.discardChangesTitle),
        content: Text(AppLocalizations.of(context)!.discardChangesContent),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(AppLocalizations.of(context)!.keepEditing),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).colorScheme.error),
            child: Text(AppLocalizations.of(context)!.discard),
          ),
        ],
      ),
    );

    return confirmed ?? false;
  }

  @override
  Widget build(BuildContext context) {
    final state = context.watch<AppState>();
    final brand = state.activeBrandTheme;

    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: brand.brandColor,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
          title: Text(AppLocalizations.of(context)!.photoToPdf),
          actions: [
            IconButton(
              onPressed: _undoStack.isEmpty || _saving || !_hasUserActions ? null : _showUndoConfirmation,
              icon: const Icon(Icons.undo),
              tooltip: AppLocalizations.of(context)!.undo,
            )
          ],
        ),
        body: Column(
        children: [
          // Main grid area
          Expanded(
            child: Container(
              width: double.infinity,
              color: Colors.grey.shade100,
              padding: const EdgeInsets.all(16),
              child: _images.isEmpty
                  ? _buildEmptyState()
                  : _buildImageGrid(),
            ),
          ),
          // Bottom action area
          Container(
            padding: const EdgeInsets.all(24),
            color: Theme.of(context).scaffoldBackgroundColor,
            child: _buildBottomActions(brand),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 200,
            height: 280,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade400, width: 2),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.crop_free,
                  size: 48,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(height: 16),
                Icon(
                  Icons.add,
                  size: 32,
                  color: Colors.grey.shade600,
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          Text(
            AppLocalizations.of(context)!.photoToPdfSubtitle,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageGrid() {
    final children = <Widget>[];
    
    // Add image thumbnails
    children.addAll(_images.asMap().entries.map((entry) {
      final index = entry.key;
      final image = entry.value;
      return _ImageThumbnail(
        key: ValueKey(image.path),
        image: image,
        index: index,
        onCrop: () => _cropImage(index),
        onDelete: () => _showDeleteConfirmation(index),
        onTap: () => _openImagePreview(index),
      );
    }));
    
    // Add placeholder for adding more images
    children.add(_AddMorePlaceholder(
      key: const ValueKey('add_more'),
      onTap: _showAddMoreOptions,
    ));
    
    return ReorderableGridView.count(
      crossAxisCount: _getCrossAxisCount(),
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      children: children,
      onReorder: (oldIndex, newIndex) {
        // Don't allow reordering the add more placeholder
        if (oldIndex >= _images.length || newIndex >= _images.length) return;
        
        _pushUndoOperation(
          OperationType.reorderImages,
          AppLocalizations.of(context)!.reorderImage,
        );
        setState(() {
          if (newIndex > oldIndex) newIndex -= 1;
          final item = _images.removeAt(oldIndex);
          _images.insert(newIndex, item);
        });
      },
    );
  }

  int _getCrossAxisCount() {
    final width = MediaQuery.of(context).size.width;
    if (width > 600) return 4;
    if (width > 400) return 3;
    return 2;
  }

  Widget _buildBottomActions(BrandTheme brand) {
    return Row(
      children: [
        // Gallery button
        _buildCircularButton(
          icon: Icons.photo_library_outlined,
          onPressed: _pickFromGallery,
        ),
        const SizedBox(width: 16),
        // Camera button  
        _buildCircularButton(
          icon: Icons.camera_alt_outlined,
          onPressed: _takePicture,
        ),
        const SizedBox(width: 24),
        // Convert button
        Expanded(
          child: SizedBox(
            height: 56,
            child: ElevatedButton(
              onPressed: _images.isEmpty || _saving ? null : _savePdf,
              style: ElevatedButton.styleFrom(
                backgroundColor: brand.brandColor,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(28),
                ),
                elevation: 2,
              ),
              child: _saving
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      AppLocalizations.of(context)!.convert,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCircularButton({required IconData icon, required VoidCallback onPressed}) {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: Colors.grey.shade300, width: 2),
        color: Colors.white,
      ),
      child: IconButton(
        icon: Icon(icon, color: Colors.grey.shade700),
        onPressed: onPressed,
        iconSize: 24,
      ),
    );
  }

  // Initial methods for entry from camera/gallery (don't count as user actions)
  Future<void> _takePictureInitial() async {
    try {
      final img = await _picker.pickImage(source: ImageSource.camera, imageQuality: 95);
      if (img != null) {
        setState(() {
          _images.add(File(img.path));
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('[EditorScreen] Error taking initial picture: $e');
      }
    }
  }

  Future<void> _pickFromGalleryInitial() async {
    try {
      final imgs = await _picker.pickMultiImage(imageQuality: 95);
      if (imgs.isNotEmpty) {
        setState(() {
          _images.addAll(imgs.map((e) => File(e.path)));
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('[EditorScreen] Error picking initial from gallery: $e');
      }
    }
  }

  // User-initiated methods (count as user actions)
  Future<void> _takePicture() async {
    try {
      final img = await _picker.pickImage(source: ImageSource.camera, imageQuality: 95);
      if (img != null) {
        _pushUndoOperation(
          OperationType.takePicture,
          AppLocalizations.of(context)!.takePicture,
        );
        setState(() {
          _images.add(File(img.path));
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('[EditorScreen] Error taking picture: $e');
      }
    }
  }

  Future<void> _pickFromGallery() async {
    try {
      final imgs = await _picker.pickMultiImage(imageQuality: 95);
      if (imgs.isNotEmpty) {
        _pushUndoOperation(
          OperationType.addImage,
          AppLocalizations.of(context)!.addImagesFromGallery(imgs.length),
        );
        setState(() {
          _images.addAll(imgs.map((e) => File(e.path)));
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('[EditorScreen] Error picking from gallery: $e');
      }
    }
  }

  void _showAddMoreOptions() {
    final theme = Theme.of(context);
    showModalBottomSheet(
      context: context,
      backgroundColor: theme.colorScheme.surface,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt_outlined),
              title: Text(AppLocalizations.of(context)!.takePhoto),
              iconColor: theme.colorScheme.onSurfaceVariant,
              textColor: theme.colorScheme.onSurface,
              onTap: () {
                Navigator.pop(context);
                _takePicture();
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library_outlined),
              title: Text(AppLocalizations.of(context)!.importFromAlbum),
              iconColor: theme.colorScheme.onSurfaceVariant,
              textColor: theme.colorScheme.onSurface,
              onTap: () {
                Navigator.pop(context);
                _pickFromGallery();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _openImagePreview(int initialIndex) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (_) => ImagePreviewScreen(
          images: _images,
          initialIndex: initialIndex,
        ),
      ),
    );
  }

  Future<void> _cropImage(int index) async {
    final image = _images[index];
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (_) => CropScreen(image: image),
      ),
    );
    
    if (result != null && result is File) {
      _pushUndoOperation(
        OperationType.cropImage,
        AppLocalizations.of(context)!.cropImage,
        metadata: {'index': index},
      );
      setState(() {
        _images[index] = result;
      });
    }
  }

  Future<void> _showDeleteConfirmation(int index) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.deleteImage),
        content: Text(AppLocalizations.of(context)!.confirmDeleteImage),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(AppLocalizations.of(context)!.cancel),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(AppLocalizations.of(context)!.delete),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      _pushUndoOperation(
        OperationType.deleteImage,
        AppLocalizations.of(context)!.deleteImage,
        metadata: {'index': index, 'deletedImage': _images[index].path},
      );
      setState(() {
        _images.removeAt(index);
      });
    }
  }

  Future<void> _showUndoConfirmation() async {
    final description = _getUndoDescription();
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.undo),
        content: Text(AppLocalizations.of(context)!.confirmUndo(description)),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(AppLocalizations.of(context)!.cancel),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(AppLocalizations.of(context)!.undo),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      _performUndo();
    }
  }

  void _performUndo() {
    if (_undoStack.isEmpty) return;
    
    final operation = _undoStack.removeLast();
    setState(() {
      _images.clear();
      _images.addAll(operation.previousImages);
    });
  }

  Future<void> _savePdf() async {
    setState(() => _saving = true);
    try {
      final pdf = pw.Document();
      for (final imgFile in _images) {
        final bytes = await imgFile.readAsBytes();
        final image = pw.MemoryImage(bytes);
        pdf.addPage(
          pw.Page(
            build: (context) => pw.Center(child: pw.Image(image, fit: pw.BoxFit.contain)),
          ),
        );
      }
      final dir = await getApplicationDocumentsDirectory();
      final outPath = p.join(dir.path, 'scan_${DateTime.now().millisecondsSinceEpoch}.pdf');
      final outFile = File(outPath);
      await outFile.writeAsBytes(await pdf.save());

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.pdfSavedSuccessfully)),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (kDebugMode) {
        print('[EditorScreen] Error saving PDF: $e');
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.pdfSaveFailed)),
        );
      }
    } finally {
      if (mounted) setState(() => _saving = false);
    }
  }
}

class _ImageThumbnail extends StatelessWidget {
  final File image;
  final int index;
  final VoidCallback onCrop;
  final VoidCallback onDelete;
  final VoidCallback onTap;

  const _ImageThumbnail({
    super.key,
    required this.image,
    required this.index,
    required this.onCrop,
    required this.onDelete,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Image with tap detector
          GestureDetector(
            onTap: onTap,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: AspectRatio(
                  aspectRatio: 1.0,
                  child: Image.file(
                    image,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
          ),
          // Crop button bottom-left
          Positioned(
            bottom: 8,
            left: 8,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.9),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 3,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: IconButton(
                icon: const Icon(Icons.crop, color: Colors.grey),
                onPressed: onCrop,
                iconSize: 16,
                padding: const EdgeInsets.all(6),
                constraints: const BoxConstraints(
                  minWidth: 32,
                  minHeight: 32,
                ),
              ),
            ),
          ),
          // Delete button top-right (smaller)
          Positioned(
            top: 4,
            right: 4,
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: IconButton(
                icon: const Icon(Icons.close, color: Colors.white),
                onPressed: onDelete,
                iconSize: 12,
                padding: const EdgeInsets.all(2),
                constraints: const BoxConstraints(
                  minWidth: 20,
                  minHeight: 20,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _AddMorePlaceholder extends StatelessWidget {
  final VoidCallback onTap;

  const _AddMorePlaceholder({
    super.key,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.grey.shade300,
            width: 2,
            style: BorderStyle.solid,
          ),
          color: Colors.grey.shade50,
        ),
        child: AspectRatio(
          aspectRatio: 1.0,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.add,
                size: 32,
                color: Colors.grey.shade600,
              ),
              const SizedBox(height: 8),
              Text(
                AppLocalizations.of(context)!.addMoreImages,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Full-screen image preview
class ImagePreviewScreen extends StatefulWidget {
  final List<File> images;
  final int initialIndex;

  const ImagePreviewScreen({
    super.key,
    required this.images,
    required this.initialIndex,
  });

  @override
  State<ImagePreviewScreen> createState() => _ImagePreviewScreenState();
}

class _ImagePreviewScreenState extends State<ImagePreviewScreen> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: Text('${_currentIndex + 1} / ${widget.images.length}'),
        elevation: 0,
      ),
      body: PageView.builder(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        itemCount: widget.images.length,
        itemBuilder: (context, index) {
          return InteractiveViewer(
            child: Center(
              child: Image.file(
                widget.images[index],
                fit: BoxFit.contain,
              ),
            ),
          );
        },
      ),
    );
  }
}

// ReorderableGridView implementation
class ReorderableGridView extends StatefulWidget {
  final List<Widget> children;
  final Function(int, int) onReorder;
  final int crossAxisCount;
  final double crossAxisSpacing;
  final double mainAxisSpacing;

  const ReorderableGridView.count({
    super.key,
    required this.children,
    required this.onReorder,
    required this.crossAxisCount,
    this.crossAxisSpacing = 0,
    this.mainAxisSpacing = 0,
  });

  @override
  State<ReorderableGridView> createState() => _ReorderableGridViewState();
}

class _ReorderableGridViewState extends State<ReorderableGridView> {
  @override
  Widget build(BuildContext context) {
    return GridView.count(
      crossAxisCount: widget.crossAxisCount,
      crossAxisSpacing: widget.crossAxisSpacing,
      mainAxisSpacing: widget.mainAxisSpacing,
      children: widget.children.asMap().entries.map((entry) {
        final index = entry.key;
        final child = entry.value;
        
        return LongPressDraggable<int>(
          data: index,
          feedback: Material(
            elevation: 4,
            borderRadius: BorderRadius.circular(12),
            child: SizedBox(
              width: 100,
              height: 100,
              child: child,
            ),
          ),
          childWhenDragging: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.grey.shade300,
            ),
          ),
          child: DragTarget<int>(
            onAcceptWithDetails: (details) {
              final draggedIndex = details.data;
              if (draggedIndex != index) {
                widget.onReorder(draggedIndex, index);
              }
            },
            builder: (context, candidateData, rejectedData) {
              return child;
            },
          ),
        );
      }).toList(),
    );
  }
}