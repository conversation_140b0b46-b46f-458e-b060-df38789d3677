import 'dart:io';

import 'package:flutter/material.dart';
import 'package:path/path.dart' as p;
import 'package:provider/provider.dart';

import '../../l10n/app_localizations.dart';
import '../services/document_scanner.dart';
import '../state/app_state.dart';
import '../widgets/file_tile.dart';

class RecentsScreen extends StatelessWidget {
  const RecentsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final state = context.watch<AppState>();
    final recentPaths = state.recents.paths;
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          l10n.recent,
          style: theme.textTheme.titleLarge?.copyWith(
            color: theme.colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: recentPaths.isEmpty
          ? Center(
              child: Text(
                l10n.noRecentFiles,
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            )
          : ListView.separated(
              padding: const EdgeInsets.all(12),
              itemBuilder: (context, index) {
                final path = recentPaths[index];
                final file = File(path);
                if (!file.existsSync()) {
                  return ListTile(
                    leading: const Icon(Icons.error_outline),
                    title: Text(p.basename(path)),
                    subtitle: Text(l10n.fileNotFound),
                  );
                }
                final stat = file.statSync();
                final doc = DocumentFile(
                  path: path,
                  displayName: p.basename(path),
                  sizeBytes: stat.size,
                  modified: stat.modified,
                );
                return FileTile(file: doc);
              },
              separatorBuilder: (_, __) => const SizedBox(height: 6),
              itemCount: recentPaths.length,
            ),
    );
  }
}
