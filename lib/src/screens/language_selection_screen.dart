import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../l10n/app_localizations.dart';

import '../state/app_state.dart';

class LanguageSelectionScreen extends StatelessWidget {
  const LanguageSelectionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final appState = context.watch<AppState>();
    final l10n = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.selectLanguage, style: Theme.of(context).appBarTheme.titleTextStyle?.copyWith(color: Theme.of(context).colorScheme.onSurface)),
        elevation: 0,
      ),
      body: ListView(
        children: [
          // System Language Option
          ListTile(
            leading: const Icon(Icons.settings_outlined),
            title: Text(l10n.systemLanguage, style: Theme.of(context).listTileTheme.titleTextStyle?.copyWith(color: Theme.of(context).colorScheme.onSurface)),
            subtitle: Text('${l10n.systemLanguage} (${_getSystemLanguageName()})'),
            trailing: appState.selectedLocale == null 
                ? Icon(Icons.check, color: Theme.of(context).colorScheme.primary)
                : null,
            onTap: () async {
              await appState.changeLanguage(null);
              if (context.mounted) {
                Navigator.of(context).pop();
              }
            },
          ),
          const Divider(),
          
          // Language Options
          ...AppState.supportedLocales.map((locale) {
            final isSelected = appState.selectedLocale == locale;
            final flag = appState.getLanguageFlag(locale);
            final name = appState.getLanguageName(locale);
            
            return ListTile(
              leading: Text(
                flag,
                style: const TextStyle(fontSize: 24),
              ),
              title: Text(
                name,
                style: Theme.of(context).listTileTheme.titleTextStyle?.copyWith(color: Theme.of(context).colorScheme.onSurface),
              ),
              trailing: isSelected 
                  ? Icon(Icons.check, color: Theme.of(context).colorScheme.primary)
                  : null,
              onTap: () async {
                await appState.changeLanguage(locale);
                if (context.mounted) {
                  Navigator.of(context).pop();
                }
              },
            );
          }),
        ],
      ),
    );
  }

  String _getSystemLanguageName() {
    final systemLocale = WidgetsBinding.instance.platformDispatcher.locale;
    final matchedLocale = AppState.supportedLocales.firstWhere(
      (locale) => 
          locale.languageCode == systemLocale.languageCode &&
          (locale.countryCode ?? '') == (systemLocale.countryCode ?? ''),
      orElse: () => AppState.supportedLocales.firstWhere(
        (locale) => locale.languageCode == systemLocale.languageCode,
        orElse: () => const Locale('en'),
      ),
    );
    return AppState().getLanguageName(matchedLocale);
  }

}

