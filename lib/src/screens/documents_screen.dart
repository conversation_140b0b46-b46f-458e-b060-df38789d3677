import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../app_theme.dart';
import '../state/app_state.dart';
import '../services/directory_picker_service.dart';
import '../services/authorized_directories_service.dart';
import 'search_screen.dart';
import 'editor_screen.dart';
import '../widgets/file_tile.dart';
import '../../l10n/app_localizations.dart';

class DocumentsScreen extends StatelessWidget {
  const DocumentsScreen({super.key});

  Future<void> _pickFiles(BuildContext context) async {
    final appState = context.read<AppState>();
    try {
      final result = await FilePicker.platform.pickFiles(
        allowMultiple: true,
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
      );
      if (result != null && result.files.isNotEmpty) {
        await appState.addFiles(result.files);
      }
    } catch (e) {
      // Handle exceptions
      if (kDebugMode) {
        print('Error picking files: $e');
      }
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.cannotSelectFiles)),
        );
      }
    }
  }

  Future<void> _pickDirectory(BuildContext context) async {
    try {
      if (!(Platform.isIOS || Platform.isAndroid)) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(AppLocalizations.of(context)!.directorySelectionOnlyMobile)),
          );
        }
        return;
      }

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.selectingDirectory)),
        );
      }

      if (kDebugMode) {
        print('[DocumentsScreen] Calling DirectoryPickerService.pickDirectory()');
      }
      final result = await DirectoryPickerService.pickDirectory();
      if (kDebugMode) {
        print('[DocumentsScreen] pickDirectory result: $result');
      }
      
      if (result != null && result.success) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(AppLocalizations.of(context)!.directorySelected(result.name))),
          );
        }
        
        // Add directory to authorized directories service for Settings page sync
        if (kDebugMode) {
          print('[DocumentsScreen] Adding directory to AuthorizedDirectoriesService: ${result.path}');
        }
        await AuthorizedDirectoriesService.instance.addDirectory(result.path);
        
        // Notify AppState that a directory was authorized to update UI immediately
        if (context.mounted) {
          final appState = context.read<AppState>();
          await appState.onDirectoryAuthorized();
        }
        
        if (kDebugMode) {
          print('[DocumentsScreen] About to load directory files for ${result.path}');
        }
        if (context.mounted) {
          await _loadDirectoryFiles(context, result.path);
        }
      } else {
        if (kDebugMode) {
          print('[DocumentsScreen] No result or failed: $result');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error picking directory: $e');
      }
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.cannotSelectDirectory)),
        );
      }
    }
  }

  Future<void> _loadDirectoryFiles(BuildContext context, String path) async {
    final appState = context.read<AppState>();
    try {
      if (kDebugMode) {
        print('[DocumentsScreen] Calling DirectoryPickerService.getDirectoryFilesForPath()');
      }
      final files = await DirectoryPickerService.getDirectoryFilesForPath(path);
      if (kDebugMode) {
        print('[DocumentsScreen] getDirectoryFiles returned: ${files?.length ?? 0} files');
      }
      
      if (files != null) {
        if (kDebugMode) {
          print('[DocumentsScreen] About to call appState.addDirectoryFiles with ${files.length} files');
        }
        await appState.addDirectoryFiles(files);
        if (kDebugMode) {
          print('[DocumentsScreen] Finished adding directory files to app state');
        }
        
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(AppLocalizations.of(context)!.loadedFiles(files.length))),
          );
        }
      } else {
        if (kDebugMode) {
          print('[DocumentsScreen] getDirectoryFiles returned null');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading directory files: $e');
      }
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.cannotLoadDirectoryFiles)),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = context.watch<AppState>();
    final brand = state.activeBrandTheme;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: brand.brandColor,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        title: const Text('All PDF Editor'),
        actions: [
          IconButton(
            tooltip: 'Sort',
            icon: const Icon(Icons.sort),
            onPressed: () async {
              await _showSortSheet(context);
            },
          ),
          IconButton(
            tooltip: 'Search',
            icon: const Icon(Icons.search),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(builder: (_) => const SearchScreen()),
              );
            },
          ),
          IconButton(
            tooltip: 'Edit',
            icon: const Icon(Icons.edit_square),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(builder: (_) => const EditorScreen()),
              );
            },
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Column(
        children: [
          _TypeTabs(brand: brand),
          const SizedBox(height: 8),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () => context.read<AppState>().refreshScan(),
              child: _FilesList(
                onPickFiles: () => _pickFiles(context),
                onPickDirectory: () => _pickDirectory(context),
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: _FabCreatePdf(
        onPickFiles: () => _pickFiles(context),
        onPickDirectory: () => _pickDirectory(context),
      ),
    );
  }
}

class _TypeTabs extends StatelessWidget {
  final BrandTheme brand;
  const _TypeTabs({required this.brand});

  @override
  Widget build(BuildContext context) {
    final state = context.watch<AppState>();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: SegmentedButton<DocType>(
        segments: const [
          ButtonSegment(value: DocType.pdf, icon: Icon(Icons.picture_as_pdf), label: Text('PDF')),
          ButtonSegment(value: DocType.word, icon: Icon(Icons.text_snippet), label: Text('Word')),
          ButtonSegment(value: DocType.excel, icon: Icon(Icons.grid_on), label: Text('Excel')),
          ButtonSegment(value: DocType.ppt, icon: Icon(Icons.slideshow), label: Text('PPT')),
        ],
        selected: <DocType>{state.activeDocType},
        onSelectionChanged: (set) {
          final type = set.first;
          context.read<AppState>().setDocType(type);
        },
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.resolveWith((states) {
            return states.contains(WidgetState.selected)
                ? brand.brandColor.withValues(alpha: 0.12)
                : Theme.of(context).colorScheme.surface;
          }),
          foregroundColor: WidgetStateProperty.resolveWith((states) {
            return states.contains(WidgetState.selected)
                ? brand.brandColor
                : Theme.of(context).colorScheme.onSurfaceVariant;
          }),
        ),
      ),
    );
  }
}

class _FilesList extends StatelessWidget {
  final VoidCallback onPickFiles;
  final VoidCallback onPickDirectory;
  const _FilesList({required this.onPickFiles, required this.onPickDirectory});

  @override
  Widget build(BuildContext context) {
    final state = context.watch<AppState>();
    final files = state.currentDocuments;

    if (!state.initialized) {
      return const Center(child: CircularProgressIndicator());
    }

    if (files.isEmpty) {
      if (Platform.isIOS) {
        if (!state.hasAuthorizedDirectories) {
          // No authorized directories - show iOS permission message
          return LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                padding: const EdgeInsets.all(24.0),
                child: ConstrainedBox(
                  constraints: BoxConstraints(minHeight: constraints.maxHeight - 48),
                  child: Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          AppLocalizations.of(context)!.iosPermissionMessage,
                          textAlign: TextAlign.center,
                          style: const TextStyle(fontSize: 16),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            ElevatedButton.icon(
                              icon: const Icon(Icons.folder_special_outlined),
                              label: Text(AppLocalizations.of(context)!.authorizeFolder),
                              onPressed: onPickDirectory,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          );
        } else {
          // Has authorized directories but no files found - show no files message
          return LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(24.0),
                child: ConstrainedBox(
                  constraints: BoxConstraints(minHeight: constraints.maxHeight - 48),
                  child: Center(
                    child: Text(
                      AppLocalizations.of(context)!.noFilesFoundMessage,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              );
            },
          );
        }
      } else {
        return LayoutBuilder(
          builder: (context, constraints) {
            return SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(24.0),
              child: ConstrainedBox(
                constraints: BoxConstraints(minHeight: constraints.maxHeight - 48),
                child: Center(
                  child: Text(
                    AppLocalizations.of(context)!.noFilesFoundGeneral,
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            );
          },
        );
      }
    }

    return ListView.separated(
      physics: const AlwaysScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      itemBuilder: (context, index) {
        final f = files[index];
        return FileTile(file: f);
      },
      separatorBuilder: (_, __) => const SizedBox(height: 4),
      itemCount: files.length,
    );
  }
}

class _FabCreatePdf extends StatelessWidget {
  final VoidCallback onPickFiles;
  final VoidCallback onPickDirectory;
  const _FabCreatePdf({required this.onPickFiles, required this.onPickDirectory});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    final menuTextStyle = textTheme.titleMedium?.copyWith(color: colorScheme.onSurface);

    return FloatingActionButton(
      child: const Icon(Icons.add),
      onPressed: () async {
        await showModalBottomSheet(
          context: context,
          showDragHandle: true,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
          ),
          builder: (context) {
            return SafeArea(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (Platform.isIOS) ...[
                    ListTile(
                      leading: const Icon(Icons.folder_special_outlined),
                      title: Text(AppLocalizations.of(context)!.selectFolder, style: menuTextStyle),
                      subtitle: Text(AppLocalizations.of(context)!.selectFolderSubtitle, 
                        style: TextStyle(
                          fontSize: 12, 
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      onTap: () {
                        Navigator.pop(context);
                        onPickDirectory();
                      },
                    ),
                  ],
                  ListTile(
                    leading: const Icon(Icons.camera_alt_outlined),
                    title: Text(AppLocalizations.of(context)!.photoToPdf, style: menuTextStyle),
                    subtitle: Text(AppLocalizations.of(context)!.photoToPdfSubtitle, 
                      style: TextStyle(
                        fontSize: 12, 
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.of(context).push(
                        MaterialPageRoute(builder: (_) => const EditorScreen(fromCamera: true)),
                      );
                    },
                  ),
                  ListTile(
                    leading: const Icon(Icons.image_outlined),
                    title: Text(AppLocalizations.of(context)!.mergeImagesToPdf, style: menuTextStyle),
                    subtitle: Text(AppLocalizations.of(context)!.mergeImagesToPdfSubtitle, 
                      style: TextStyle(
                        fontSize: 12, 
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.of(context).push(
                        MaterialPageRoute(builder: (_) => const EditorScreen(fromGallery: true)),
                      );
                    },
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}

Future<void> _showSortSheet(BuildContext context) async {
  final state = context.read<AppState>();
  await showModalBottomSheet(
    context: context,
    showDragHandle: true,
    shape: const RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(24))),
    builder: (context) {
      var by = state.sortBy;
      var order = state.sortOrder;
      final textTheme = Theme.of(context).textTheme;
      final colorScheme = Theme.of(context).colorScheme;
      final titleStyle = textTheme.titleLarge?.copyWith(color: colorScheme.onSurface);
      final itemStyle = textTheme.titleMedium?.copyWith(color: colorScheme.onSurface);

      return StatefulBuilder(builder: (context, setState) {
        return SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(AppLocalizations.of(context)!.sort, style: titleStyle),
                const SizedBox(height: 8),
                RadioListTile<SortBy>(
                  value: SortBy.name,
                  groupValue: by,
                  title: Text(AppLocalizations.of(context)!.nameSort, style: itemStyle),
                  onChanged: (v) => setState(() => by = v!),
                ),
                RadioListTile<SortBy>(
                  value: SortBy.modified,
                  groupValue: by,
                  title: Text(AppLocalizations.of(context)!.lastModified, style: itemStyle),
                  onChanged: (v) => setState(() => by = v!),
                ),
                RadioListTile<SortBy>(
                  value: SortBy.size,
                  groupValue: by,
                  title: Text(AppLocalizations.of(context)!.sizeSort, style: itemStyle),
                  onChanged: (v) => setState(() => by = v!),
                ),
                const Divider(),
                SwitchListTile(
                  value: order == SortOrder.desc,
                  onChanged: (v) => setState(() => order = v ? SortOrder.desc : SortOrder.asc),
                  title: Text(AppLocalizations.of(context)!.descending, style: itemStyle),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text(AppLocalizations.of(context)!.cancel),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: FilledButton(
                        onPressed: () {
                          context.read<AppState>().setSorting(by, order);
                          Navigator.pop(context);
                        },
                        child: Text(AppLocalizations.of(context)!.apply),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
              ],
            ),
          ),
        );
      });
    },
  );
}
