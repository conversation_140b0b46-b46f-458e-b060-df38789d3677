import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../l10n/app_localizations.dart';

import '../state/app_state.dart';
import 'about_screen.dart';
import 'authorized_directories_screen.dart';
import 'language_selection_screen.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  Future<String> _getDirectorySubtitle(AppLocalizations l10n) async {
    if (Platform.isIOS) {
      try {
        final deviceInfo = DeviceInfoPlugin();
        final iosInfo = await deviceInfo.iosInfo;
        final isIPad = iosInfo.model.toLowerCase().contains('ipad');
        
        if (isIPad) {
          return l10n.selectAuthorizedDirectorySubtitle.replaceAll('iPhone/iPad', 'iPad');
        } else {
          return l10n.selectAuthorizedDirectorySubtitle.replaceAll('iPhone/iPad', 'iPhone');
        }
      } catch (e) {
        if (kDebugMode) {
          print('[SettingsScreen] Error getting device info: $e');
        }
        return l10n.selectAuthorizedDirectorySubtitle;
      }
    } else {
      // Android subtitle
      return '管理和添加可访问的文件夹';
    }
  }

  void _openAuthorizedDirectories(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AuthorizedDirectoriesScreen(),
      ),
    );
  }

  Future<void> _shareApp(BuildContext context) async {
    final l10n = AppLocalizations.of(context)!;
    try {
      String appStoreLink;
      if (Platform.isIOS) {
        appStoreLink = 'https://apps.apple.com/us/app/kjv-bible-now-read-study/id6447543328';
      } else {
        appStoreLink = 'https://play.google.com/store/apps/details?id=com.facebook.katana';
      }
      
      await Share.share(
        '${l10n.shareAppSubtitle}\n$appStoreLink',
        subject: l10n.appName,
      );
    } catch (e) {
      if (kDebugMode) {
        print('[SettingsScreen] Error sharing app: $e');
      }
    }
  }

  Future<void> _rateApp(BuildContext context) async {
    _showRatingDialog(context);
  }

  void _showRatingDialog(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Theme.of(context).colorScheme.surface,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          content: ConstrainedBox(
            constraints: const BoxConstraints(maxHeight: 500),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 20.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                // Happy face emoji
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.yellow.shade400,
                    shape: BoxShape.circle,
                  ),
                  child: const Center(
                    child: Text(
                      '😊',
                      style: TextStyle(fontSize: 30),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                // Title
                Text(
                  l10n.weLikeYouToo,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                // Subtitle
                Text(
                  l10n.thankYouForFeedback,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                // Stars
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(5, (index) => 
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 2),
                      child: Icon(
                        Icons.star,
                        color: Colors.red.shade700,
                        size: 28,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 6),
                // Best rating text
                Text(
                  l10n.theBestWeCanGet,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.red.shade700,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                // Buttons
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: TextButton.styleFrom(
                          backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                          foregroundColor: Theme.of(context).colorScheme.onSurface,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                        child: Text(
                          l10n.maybeLater,
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          _launchAppStore(context);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red.shade700,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                        child: Text(
                          l10n.rateNow,
                          style: const TextStyle(fontWeight: FontWeight.w600),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> _launchAppStore(BuildContext context) async {
    try {
      String appStoreLink;
      if (Platform.isIOS) {
        appStoreLink = 'https://apps.apple.com/us/app/kjv-bible-now-read-study/id6447543328';
      } else {
        appStoreLink = 'https://play.google.com/store/apps/details?id=com.facebook.katana';
      }
      
      final Uri uri = Uri.parse(appStoreLink);
      if (!await launchUrl(uri)) {
        if (kDebugMode) {
          print('[SettingsScreen] Could not launch app store URL');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('[SettingsScreen] Error launching app store: $e');
      }
    }
  }

  Widget _buildSettingsItem({
    required IconData icon,
    required String title,
    required String subtitle,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(icon),
            const SizedBox(width: 16.0),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(title, style: const TextStyle(fontSize: 16.0)),
                  if (subtitle.isNotEmpty)
                    Text(subtitle, style: const TextStyle(color: Colors.grey)),
                ],
              ),
            ),
            if (trailing != null) trailing,
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final appState = context.watch<AppState>();
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          l10n.settings,
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
      ),
      body: ListView(
        children: [
          // 1. Language Settings (moved to first)
          _buildSettingsItem(
            icon: Icons.language_outlined,
            title: l10n.languageSettings,
            subtitle: _getCurrentLanguageText(appState, l10n),
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const LanguageSelectionScreen(),
                ),
              );
            },
          ),
          const Divider(),

          // 2. Select Authorized Directory
          FutureBuilder<String>(
            future: _getDirectorySubtitle(l10n),
            builder: (context, snapshot) {
              final subtitle = snapshot.data ?? l10n.selectAuthorizedDirectorySubtitle;
              return _buildSettingsItem(
                icon: Icons.folder_outlined,
                title: l10n.selectAuthorizedDirectory,
                subtitle: subtitle,
                trailing: const Icon(Icons.chevron_right),
                onTap: () => _openAuthorizedDirectories(context),
              );
            },
          ),
          const Divider(),

          // 3. Share
          _buildSettingsItem(
            icon: Icons.share_outlined,
            title: l10n.shareApp,
            subtitle: l10n.shareAppSubtitle,
            trailing: const Icon(Icons.chevron_right),
            onTap: () => _shareApp(context),
          ),
          const Divider(),

          // 4. Rate
          _buildSettingsItem(
            icon: Icons.star_outline,
            title: l10n.rateApp,
            subtitle: l10n.rateAppSubtitle,
            trailing: const Icon(Icons.chevron_right),
            onTap: () => _rateApp(context),
          ),
          const Divider(),

          // 5. About
          _buildSettingsItem(
            icon: Icons.info_outline,
            title: l10n.aboutApp,
            subtitle: l10n.aboutAppSubtitle,
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const AboutScreen(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  String _getCurrentLanguageText(AppState appState, AppLocalizations l10n) {
    if (appState.selectedLocale == null) {
      return l10n.systemLanguage;
    } else {
      final languageName = appState.getLanguageName(appState.selectedLocale!);
      return l10n.currentLanguage(languageName);
    }
  }
}
