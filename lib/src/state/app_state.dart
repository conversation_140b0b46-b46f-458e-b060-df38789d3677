import 'dart:async';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../app_theme.dart';
import '../services/document_scanner.dart';
import '../services/recents_service.dart';
import '../services/favorites_service.dart';
import '../services/directory_picker_service.dart';
import '../services/authorized_directories_service.dart';

enum DocType { pdf, word, excel, ppt }

enum SortBy { name, modified, size }

enum SortOrder { asc, desc }

class AppState extends ChangeNotifier {
  AppState();

  // UI State
  int bottomTabIndex = 0; // 0 Document, 1 Recent, 2 Favourite, 3 Setting
  DocType activeDocType = DocType.pdf;
  SortBy sortBy = SortBy.name;
  SortOrder sortOrder = SortOrder.asc;
  
  // Language State
  Locale? _selectedLocale;
  Locale? get selectedLocale => _selectedLocale;

  BrandTheme get activeBrandTheme {
    switch (activeDocType) {
      case DocType.pdf:
        return BrandThemes.pdf;
      case DocType.word:
        return BrandThemes.word;
      case DocType.excel:
        return BrandThemes.excel;
      case DocType.ppt:
        return BrandThemes.ppt;
    }
  }

  // Data
  final DocumentScanner _scanner = DocumentScanner();
  final RecentsService _recentsService = RecentsService();
  final FavoritesService _favoritesService = FavoritesService();

  bool _initialized = false;
  bool get initialized => _initialized;

  // Flag to indicate if the user has added files at least once on iOS.
  bool _iosFilesAdded = false;
  bool get iosFilesAdded => _iosFilesAdded;

  // Track if there are any authorized directories (synchronous state)
  bool _hasAuthorizedDirectories = false;
  bool get hasAuthorizedDirectories => _hasAuthorizedDirectories;

  // Update the authorized directories status
  Future<void> _updateAuthorizedDirectoriesStatus() async {
    try {
      final directories = await AuthorizedDirectoriesService.instance.getAuthorizedDirectories();
      final newStatus = directories.isNotEmpty;
      if (_hasAuthorizedDirectories != newStatus) {
        if (kDebugMode) {
          print('[AppState] Authorized directories status changed: $_hasAuthorizedDirectories -> $newStatus');
        }
        _hasAuthorizedDirectories = newStatus;
        notifyListeners();
      }
    } catch (e) {
      if (kDebugMode) {
        print('[AppState] Error updating authorized directories status: $e');
      }
    }
  }

  // Call this when a directory is authorized from outside
  Future<void> onDirectoryAuthorized() async {
    await _updateAuthorizedDirectoriesStatus();
  }

  Future<void> initialize() async {
    await _recentsService.initialize();
    await _favoritesService.initialize();
    await _scanner.initialize();
    await _loadSelectedLanguage();
    await _updateAuthorizedDirectoriesStatus(); // Initialize authorization status
    _initialized = true;
    notifyListeners();
    // On Android, start a background scan. On iOS, we wait for user action.
    if (Platform.isAndroid) {
      unawaited(refreshScan());
    } else if (Platform.isIOS) {
      // On iOS, try to auto-load files from previously selected directory
      unawaited(_loadSavedDirectoryFiles());
    }
  }

  Future<void> _loadSavedDirectoryFiles() async {
    try {
      // Add a small delay to ensure method channel is properly initialized
      await Future.delayed(const Duration(milliseconds: 500));
      
      // First check if there are any authorized directories in SharedPreferences
      final authorizedDirs = await AuthorizedDirectoriesService.instance.getAuthorizedDirectories();
      if (kDebugMode) {
        print('[AppState] Found ${authorizedDirs.length} authorized directories in SharedPreferences');
      }
      
      // Only load files if there are authorized directories
      if (authorizedDirs.isNotEmpty) {
        final files = await DirectoryPickerService.getDirectoryFiles();
        if (files != null && files.isNotEmpty) {
          // Filter files to only include those from currently authorized directories
          final validFiles = files.where((file) {
            return authorizedDirs.any((authorizedDir) => 
              file.path.startsWith(authorizedDir));
          }).toList();
          
          if (kDebugMode) {
            print('[AppState] Filtered ${validFiles.length} valid files from ${files.length} total files');
            if (validFiles.length != files.length) {
              print('[AppState] Some files were from unauthorized directories and excluded');
            }
          }
          
          if (validFiles.isNotEmpty) {
            await addDirectoryFiles(validFiles);
            if (kDebugMode) {
              print('[AppState] Auto-loaded ${validFiles.length} files from authorized directories.');
            }
          }
        }
      } else {
        if (kDebugMode) {
          print('[AppState] No authorized directories found, skipping auto-load and clearing any stale iOS bookmarks.');
        }
        // Clear any stale iOS bookmarks that might exist
        await DirectoryPickerService.clearAllBookmarks();
      }
    } catch (e) {
      if (kDebugMode) {
        print('[AppState] Failed to auto-load directory files: $e');
      }
    }
  }

  Future<void> addFiles(List<PlatformFile> files) async {
    if (kDebugMode) {
      print('[AppState] Adding ${files.length} files from picker.');
    }
    final newDocs = <DocumentFile>[];
    for (final file in files) {
      // The path from file_picker on iOS/Android is a real, accessible path for the selected file.
      if (file.path != null) {
        final doc = DocumentFile(
          path: file.path!,
          displayName: file.name,
          sizeBytes: file.size,
          // Stat-ing the file might not be needed if picker provides metadata,
          // but for modification date, it's more reliable.
          modified: await File(file.path!).lastModified(),
        );
        newDocs.add(doc);
      }
    }
    _scanner.addFiles(newDocs);
    _iosFilesAdded = true;
    notifyListeners();
  }

  Future<void> addDirectoryFiles(List<FileInfo> files) async {
    if (kDebugMode) {
      print('[AppState] Adding ${files.length} files from directory picker.');
    }
    final newDocs = <DocumentFile>[];
    for (final file in files) {
      // Filter only supported document types
      final extension = file.extension.toLowerCase();
      if (kDebugMode) {
        print('[AppState] Processing file: ${file.name}, extension: $extension, supported: ${_isSupportedFileType(extension)}');
      }
      if (_isSupportedFileType(extension)) {
        final doc = DocumentFile(
          path: file.path,
          displayName: file.name,
          sizeBytes: file.size,
          modified: file.modificationDate ?? DateTime.now(),
        );
        newDocs.add(doc);
        if (kDebugMode) {
          print('[AppState] Added document: ${file.name}');
        }
      }
    }
    if (kDebugMode) {
      print('[AppState] Filtered ${newDocs.length} supported documents from ${files.length} total files');
    }
    if (newDocs.isNotEmpty) {
      _scanner.addFiles(newDocs);
      _iosFilesAdded = true;
      if (kDebugMode) {
        print('[AppState] Called scanner.addFiles and notifyListeners');
      }
      notifyListeners();
    } else {
      if (kDebugMode) {
        print('[AppState] No supported documents found, not updating UI');
      }
    }
  }

  bool _isSupportedFileType(String extension) {
    const supportedExtensions = {
      'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'
    };
    return supportedExtensions.contains(extension);
  }

  Future<void> refreshScan() async {
    if (Platform.isAndroid) {
      if (kDebugMode) {
        print('[AppState] Starting refreshScan on Android.');
      }
      await _scanner.scan();
    } else if (Platform.isIOS) {
      if (kDebugMode) {
        print('[AppState] Starting refreshScan on iOS.');
      }
      
      // First clear all existing files to ensure clean state
      _scanner.clearAll();
      if (kDebugMode) {
        print('[AppState] Cleared all existing files from scanner.');
      }
      
      // On iOS, a "refresh" means re-scanning all authorized directories.
      // First check current authorized directories
      final authorizedDirs = await AuthorizedDirectoriesService.instance.getAuthorizedDirectories();
      if (kDebugMode) {
        print('[AppState] Current authorized directories: ${authorizedDirs.length}');
      }
      
      if (authorizedDirs.isEmpty) {
        if (kDebugMode) {
          print('[AppState] No authorized directories, clearing iOS bookmarks and keeping scanner empty.');
        }
        await DirectoryPickerService.clearAllBookmarks();
        // Reset the iosFilesAdded flag when no authorized directories exist
        _iosFilesAdded = false;
        // Update authorization status
        await _updateAuthorizedDirectoriesStatus();
        return; // Scanner is already cleared, just return
      }
      
      final files = await DirectoryPickerService.getDirectoryFiles();
      if (kDebugMode) {
        print('[AppState] getDirectoryFiles returned ${files?.length ?? 0} files.');
      }
      
      final newDocs = <DocumentFile>[];
      if (files != null && files.isNotEmpty) {
        // Filter files to only include those from currently authorized directories
        final validFiles = files.where((file) {
          return authorizedDirs.any((authorizedDir) => 
            file.path.startsWith(authorizedDir));
        }).toList();
        
        if (kDebugMode) {
          print('[AppState] Filtered ${validFiles.length} valid files from ${files.length} total files');
        }
        
        for (final file in validFiles) {
          if (_isSupportedFileType(file.extension.toLowerCase())) {
            final doc = DocumentFile(
              path: file.path,
              displayName: file.name,
              sizeBytes: file.size,
              modified: file.modificationDate ?? DateTime.now(),
            );
            newDocs.add(doc);
          }
        }
        if (kDebugMode) {
          print('[AppState] Created ${newDocs.length} DocumentFiles from ${validFiles.length} valid FileInfo objects.');
        }
        
        // Add the new files (don't use replace since we already cleared)
        _scanner.addFiles(newDocs, replace: false);
      } else {
        if (kDebugMode) {
          print('[AppState] No files from authorized directories, keeping scanner empty.');
        }
      }
      
      // Update authorization status after scanning
      await _updateAuthorizedDirectoriesStatus();
    }
    notifyListeners();
    if (kDebugMode) {
      print('[AppState] Refresh completed, notified listeners.');
    }
  }

  void setDocType(DocType type) {
    activeDocType = type;
    notifyListeners();
  }

  void setSorting(SortBy by, SortOrder order) {
    sortBy = by;
    sortOrder = order;
    notifyListeners();
  }

  void setBottomTab(int index) {
    bottomTabIndex = index;
    notifyListeners();
  }

  // Expose filtered, sorted lists
  List<DocumentFile> get currentDocuments {
    final all = switch (activeDocType) {
      DocType.pdf => _scanner.pdfs,
      DocType.word => _scanner.words,
      DocType.excel => _scanner.excels,
      DocType.ppt => _scanner.ppts,
    };
    final list = [...all];
    list.sort((a, b) {
      int cmp;
      switch (sortBy) {
        case SortBy.name:
          cmp = a.displayName.toLowerCase().compareTo(b.displayName.toLowerCase());
          break;
        case SortBy.modified:
          cmp = a.modified.millisecondsSinceEpoch.compareTo(b.modified.millisecondsSinceEpoch);
          break;
        case SortBy.size:
          cmp = a.sizeBytes.compareTo(b.sizeBytes);
          break;
      }
      return sortOrder == SortOrder.asc ? cmp : -cmp;
    });
    return list;
  }

  RecentsService get recents => _recentsService;
  FavoritesService get favorites => _favoritesService;
  DocumentScanner get scanner => _scanner;

  // Language Management
  static const String _languageKey = 'selected_language';
  
  static const List<Locale> supportedLocales = [
    Locale('en'), // English
    Locale('es'), // Spanish
    Locale('pt'), // Portuguese
    Locale('fr'), // French
    Locale('de'), // German
    Locale('zh'), // Chinese Simplified
    Locale('zh', 'TW'), // Chinese Traditional
    Locale('ja'), // Japanese
    Locale('ko'), // Korean
  ];

  Future<void> _loadSelectedLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    final languageCode = prefs.getString(_languageKey);
    if (languageCode != null) {
      final parts = languageCode.split('_');
      if (parts.length == 2) {
        _selectedLocale = Locale(parts[0], parts[1]);
      } else {
        _selectedLocale = Locale(parts[0]);
      }
    }
  }

  Future<void> changeLanguage(Locale? locale) async {
    _selectedLocale = locale;
    final prefs = await SharedPreferences.getInstance();
    
    if (locale != null) {
      final languageCode = locale.countryCode != null 
          ? '${locale.languageCode}_${locale.countryCode}'
          : locale.languageCode;
      await prefs.setString(_languageKey, languageCode);
    } else {
      await prefs.remove(_languageKey);
    }
    
    notifyListeners();
  }

  String getLanguageName(Locale locale) {
    if (locale.languageCode == 'en') {
      return 'English';
    }
    if (locale.languageCode == 'es') {
      return 'Español';
    }
    if (locale.languageCode == 'pt') {
      return 'Português';
    }
    if (locale.languageCode == 'fr') {
      return 'Français';
    }
    if (locale.languageCode == 'de') {
      return 'Deutsch';
    }
    if (locale.languageCode == 'zh' && locale.countryCode == 'TW') {
      return '繁體中文';
    }
    if (locale.languageCode == 'zh') {
      return '简体中文';
    }
    if (locale.languageCode == 'ja') {
      return '日本語';
    }
    if (locale.languageCode == 'ko') {
      return '한국어';
    }
    return locale.languageCode.toUpperCase();
  }

  String getLanguageFlag(Locale locale) {
    if (locale.languageCode == 'en') {
      return '🇺🇸';
    }
    if (locale.languageCode == 'es') {
      return '🇪🇸';
    }
    if (locale.languageCode == 'pt') {
      return '🇵🇹';
    }
    if (locale.languageCode == 'fr') {
      return '🇫🇷';
    }
    if (locale.languageCode == 'de') {
      return '🇩🇪';
    }
    if (locale.languageCode == 'zh' && locale.countryCode == 'TW') {
      return '🌐';
    }
    if (locale.languageCode == 'zh') {
      return '🇨🇳';
    }
    if (locale.languageCode == 'ja') {
      return '🇯🇵';
    }
    if (locale.languageCode == 'ko') {
      return '🇰🇷';
    }
    return '🌐';
  }
}
