import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../l10n/app_localizations.dart';

import '../state/app_state.dart';
import '../screens/documents_screen.dart';
import '../screens/recents_screen.dart';
import '../screens/favorites_screen.dart';
import '../screens/settings_screen.dart';

class AppShell extends StatelessWidget {
  const AppShell({super.key});

  @override
  Widget build(BuildContext context) {
    final state = context.watch<AppState>();
    final color = Theme.of(context).colorScheme.primary;
    final l10n = AppLocalizations.of(context)!;

    final pages = [
      const DocumentsScreen(),
      const RecentsScreen(),
      const FavoritesScreen(),
      const SettingsScreen(),
    ];

    return Scaffold(
      body: IndexedStack(
        index: state.bottomTabIndex,
        children: pages,
      ),
      bottomNavigationBar: NavigationBar(
        selectedIndex: state.bottomTabIndex,
        onDestinationSelected: state.setBottomTab,
        indicatorColor: color.withValues(alpha: 0.15),
        destinations: [
          NavigationDestination(icon: const Icon(Icons.folder_open), label: l10n.documents),
          NavigationDestination(icon: const Icon(Icons.history), label: l10n.recent),
          NavigationDestination(icon: const Icon(Icons.star), label: l10n.favorite),
          NavigationDestination(icon: const Icon(Icons.settings), label: l10n.settings),
        ],
      ),
    );
  }
}
