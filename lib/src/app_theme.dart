import 'package:flutter/material.dart';

ThemeData buildLightTheme(Color brandColor) {
  final colorScheme = ColorScheme.fromSeed(
    seedColor: brandColor,
    brightness: Brightness.light,
  );
  return ThemeData(
    colorScheme: colorScheme,
    useMaterial3: true,
    scaffoldBackgroundColor: colorScheme.surface,
    appBarTheme: AppBarTheme(
      elevation: 0,
      backgroundColor: colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      foregroundColor: colorScheme.onSurface,
      centerTitle: false,
      titleTextStyle: const TextStyle(
        fontSize: 22,
        fontWeight: FontWeight.w700,
      ),
    ),
    floatingActionButtonTheme: FloatingActionButtonThemeData(
      backgroundColor: colorScheme.primary,
      foregroundColor: colorScheme.onPrimary,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
    ),
    cardTheme: CardThemeData(
      elevation: 0,
      color: colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: colorScheme.outlineVariant),
      ),
    ),
    listTileTheme: ListTileThemeData(
      iconColor: colorScheme.primary,
      titleTextStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
      subtitleTextStyle: TextStyle(color: colorScheme.onSurfaceVariant),
    ),
    inputDecorationTheme: InputDecorationTheme(
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(14),
        borderSide: BorderSide(color: colorScheme.outlineVariant),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(14),
        borderSide: BorderSide(color: colorScheme.primary, width: 2),
      ),
    ),
    segmentedButtonTheme: SegmentedButtonThemeData(
      style: ButtonStyle(
        shape: WidgetStatePropertyAll(
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        ),
      ),
    ),
  );
}

class BrandTheme {
  final Color brandColor;
  final String name;
  const BrandTheme(this.name, this.brandColor);
}

class BrandThemes {
  // Inspired by Adobe and Microsoft apps
  static const pdf = BrandTheme('PDF', Color(0xFFFA0F00)); // Adobe red
  static const word = BrandTheme('Word', Color(0xFF185ABD)); // Word blue
  static const excel = BrandTheme('Excel', Color(0xFF107C41)); // Excel green
  static const ppt = BrandTheme('PowerPoint', Color(0xFFD24726)); // PowerPoint orange
}
