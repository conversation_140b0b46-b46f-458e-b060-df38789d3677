// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appName => 'Centre de Documents';

  @override
  String get documents => 'Documents';

  @override
  String get recent => 'Récents';

  @override
  String get favorite => 'Favoris';

  @override
  String get settings => 'Paramètres';

  @override
  String get addFiles => 'Ajouter des Fichiers';

  @override
  String get addFilesSubtitle =>
      'Choisir plus de fichiers de votre appareil à gérer';

  @override
  String get followDocumentTheme =>
      'Suivre le schéma de couleurs du type de document';

  @override
  String get followDocumentThemeSubtitle => 'Styles PDF/Word/Excel/PPT';

  @override
  String get languageSettings => 'Langue UI';

  @override
  String get aboutApp => 'À propos';

  @override
  String get version => 'Version';

  @override
  String get appVersion => '1.0.0';

  @override
  String get copyright => '© 2025';

  @override
  String filesAdded(int count) {
    return '$count fichiers ajoutés';
  }

  @override
  String get cannotSelectFiles =>
      'Impossible de sélectionner les fichiers, veuillez vérifier les autorisations';

  @override
  String get selectLanguage => 'Sélectionner la Langue';

  @override
  String get systemLanguage => 'Langue du Système';

  @override
  String get english => 'English';

  @override
  String get spanish => 'Español';

  @override
  String get portuguese => 'Português';

  @override
  String get french => 'Français';

  @override
  String get german => 'Deutsch';

  @override
  String get chineseSimplified => '简体中文';

  @override
  String get chineseTraditional => '繁體中文';

  @override
  String get japanese => '日本語';

  @override
  String get korean => '한국어';

  @override
  String currentLanguage(String language) {
    return 'Actuel: $language';
  }

  @override
  String get selectAuthorizedDirectory => 'Gérer le Répertoire Autorisé';

  @override
  String get selectAuthorizedDirectorySubtitle =>
      'Autoriser l\'accès aux répertoires iPhone/iPad';

  @override
  String get shareApp => 'Partager';

  @override
  String get shareAppSubtitle => 'Partagez cette app avec des amis';

  @override
  String get rateApp => 'Noter';

  @override
  String get rateAppSubtitle => 'Notez-nous sur l\'App Store';

  @override
  String get aboutAppSubtitle => 'Version de l\'app, politiques et plus';

  @override
  String get privacyPolicy => 'Politique de Confidentialité';

  @override
  String get termsOfService => 'Conditions de Service';

  @override
  String get appDescription =>
      'Un magnifique centre de documents pour les fichiers PDF, Word, Excel et PowerPoint avec recherche avancée, tri et capacités de création PDF.';

  @override
  String get authorizedDirectories => 'Gérer les Répertoires Autorisés';

  @override
  String get addDirectory => 'Ajouter Répertoire';

  @override
  String get removeDirectory => 'Supprimer';

  @override
  String get noAuthorizedDirectories => 'Aucun répertoire autorisé';

  @override
  String get noAuthorizedDirectoriesSubtitle =>
      'Ajoutez des répertoires pour accéder aux fichiers de ces emplacements';

  @override
  String get directoryAdded => 'Répertoire autorisé avec succès';

  @override
  String get directoryRemoved => 'Autorisation de répertoire supprimée';

  @override
  String get cannotAddDirectory =>
      'Impossible d\'ajouter le répertoire, veuillez réessayer';

  @override
  String appCanAccessDirectories(String appName) {
    return '$appName peut accéder aux répertoires suivants :';
  }

  @override
  String filesCount(int count) {
    return '$count fichiers';
  }

  @override
  String directorySize(String size) {
    return 'Taille : $size';
  }

  @override
  String get cancelAuthorization => 'Annuler l\'autorisation';

  @override
  String confirmCancelAuthorization(String appName, String directoryName) {
    return 'Voulez-vous vraiment annuler l\'autorisation d\'accès pour le $directoryName de $appName ? Après l\'annulation, les fichiers de ce répertoire ne seront plus visibles.';
  }

  @override
  String get noKeepIt => 'Non, garder';

  @override
  String get yesRemoveFolder => 'Oui, je n\'ai plus besoin de ce dossier';

  @override
  String get editName => 'Modifier le nom';

  @override
  String get enterCustomName => 'Saisir un nom personnalisé';

  @override
  String get customName => 'Nom personnalisé';

  @override
  String get save => 'Enregistrer';

  @override
  String get cancel => 'Annuler';

  @override
  String get nameForNewFolder =>
      'Pour une identification facile plus tard, veuillez donner un nom au dossier d\'application que vous venez de sélectionner';

  @override
  String defaultAppName(int number) {
    return 'App $number';
  }

  @override
  String get selectFiles => 'Sélectionner les Fichiers';

  @override
  String get authorizeFolder => 'Autoriser le Dossier';

  @override
  String get selectingDirectory => 'Analyse du répertoire...';

  @override
  String directorySelected(String name) {
    return 'Répertoire sélectionné : $name';
  }

  @override
  String get directorySelectionOnlyMobile =>
      'La sélection de répertoire n\'est disponible que sur iOS/Android';

  @override
  String get cannotSelectDirectory =>
      'Impossible de sélectionner le répertoire, veuillez vérifier les autorisations';

  @override
  String loadedFiles(int count) {
    return '$count fichiers scannés';
  }

  @override
  String get cannotLoadDirectoryFiles =>
      'Impossible de charger les fichiers du répertoire';

  @override
  String get iosPermissionMessage =>
      'En raison des restrictions de permissions de l\'iPhone, vous devez d\'abord autoriser l\'accès aux dossiers contenant vos fichiers PDF, Word, Excel et PPT.';

  @override
  String get noFilesFoundMessage =>
      'Aucun fichier de ce type trouvé.\nVeuillez sélectionner d\'autres types ci-dessus, ou appuyer sur le bouton \'+\' pour ajouter plus de fichiers.';

  @override
  String get noFilesFoundGeneral =>
      'Aucun fichier trouvé, tirez vers le bas pour scanner à nouveau.';

  @override
  String get importFilesFromPhone =>
      'Importer des fichiers depuis le téléphone';

  @override
  String get selectFolder => 'Ajouter un Dossier Autorisé';

  @override
  String get selectFolderSubtitle =>
      'Autoriser l\'accès au dossier pour scanner et afficher les fichiers';

  @override
  String get photoToPdf => 'Photo vers PDF';

  @override
  String get photoToPdfSubtitle =>
      'Utilisez l\'appareil photo pour prendre plusieurs photos et les fusionner en PDF';

  @override
  String get mergeImagesToPdf => 'Fusionner Images en PDF';

  @override
  String get mergeImagesToPdfSubtitle => 'Fusionner Photos d\'Album en PDF';

  @override
  String get convert => 'Convertir';

  @override
  String get pdfSavedSuccessfully => 'PDF sauvegardé avec succès';

  @override
  String get pdfSaveFailed => 'Échec de la sauvegarde PDF';

  @override
  String get undo => 'Annuler';

  @override
  String get deleteImage => 'Supprimer l\'image';

  @override
  String get confirmDeleteImage =>
      'Êtes-vous sûr de vouloir supprimer cette image?';

  @override
  String get delete => 'Supprimer';

  @override
  String get reorderImage => 'Réorganiser les images';

  @override
  String get takePicture => 'Prendre une photo';

  @override
  String addImagesFromGallery(int count) {
    return 'Ajouter $count images de la galerie';
  }

  @override
  String get cropImage => 'Recadrer l\'image';

  @override
  String confirmUndo(String action) {
    return 'Annuler: $action. Cela annulera votre dernière action. Continuer?';
  }

  @override
  String get cropPlaceholderMessage =>
      'La fonctionnalité de recadrage sera implémentée ici';

  @override
  String get cropFeatureComingSoon =>
      'Fonctionnalités de recadrage avancées bientôt disponibles!';

  @override
  String get addMoreImages => 'Ajouter plus d\'images';

  @override
  String get takePhoto => 'Prendre une photo';

  @override
  String get importFromAlbum => 'Importer de l\'album';

  @override
  String get sort => 'Trier';

  @override
  String get nameSort => 'Nom';

  @override
  String get lastModified => 'Dernière Modification';

  @override
  String get sizeSort => 'Taille';

  @override
  String get descending => 'Décroissant';

  @override
  String get apply => 'Appliquer';

  @override
  String get noRecentFiles => 'Aucun fichier récent';

  @override
  String get noFavoriteFiles =>
      'Vous n\'avez encore ajouté aucun fichier aux favoris';

  @override
  String get noFavoriteFilesHint =>
      'Vous pouvez ajouter des fichiers aux favoris en touchant l\'icône ☆ étoile à côté des fichiers sur la page des documents';

  @override
  String get fileNotFound => 'Fichier introuvable';

  @override
  String get weLikeYouToo => 'Nous vous aimons aussi !';

  @override
  String get thankYouForFeedback => 'Merci pour votre commentaire.';

  @override
  String get theBestWeCanGet => 'Le mieux que nous puissions obtenir :)';

  @override
  String get maybeLater => 'Peut-être plus tard';

  @override
  String get rateNow => 'Évaluer';

  @override
  String get discardChangesTitle => 'Discard changes?';

  @override
  String get discardChangesContent =>
      'You have unsaved changes. If you exit now, they will be lost.';

  @override
  String get discard => 'Discard';

  @override
  String get keepEditing => 'Keep Editing';
}
