// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'All Document Hub';

  @override
  String get documents => 'Documents';

  @override
  String get recent => 'Recent';

  @override
  String get favorite => 'Favorite';

  @override
  String get settings => 'Settings';

  @override
  String get addFiles => 'Add Files';

  @override
  String get addFilesSubtitle => 'Choose more files from your device to manage';

  @override
  String get followDocumentTheme => 'Follow document type color scheme';

  @override
  String get followDocumentThemeSubtitle => 'PDF/Word/Excel/PPT styles';

  @override
  String get languageSettings => 'UI Language';

  @override
  String get aboutApp => 'About';

  @override
  String get version => 'Version';

  @override
  String get appVersion => '1.0.0';

  @override
  String get copyright => '© 2025';

  @override
  String filesAdded(int count) {
    return 'Added $count files';
  }

  @override
  String get cannotSelectFiles =>
      'Cannot select files, please check permissions';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get systemLanguage => 'System Language';

  @override
  String get english => 'English';

  @override
  String get spanish => 'Español';

  @override
  String get portuguese => 'Português';

  @override
  String get french => 'Français';

  @override
  String get german => 'Deutsch';

  @override
  String get chineseSimplified => '简体中文';

  @override
  String get chineseTraditional => '繁體中文';

  @override
  String get japanese => '日本語';

  @override
  String get korean => '한국어';

  @override
  String currentLanguage(String language) {
    return 'Current: $language';
  }

  @override
  String get selectAuthorizedDirectory => 'Manage Authorized Folders';

  @override
  String get selectAuthorizedDirectorySubtitle =>
      'Authorize access to iPhone/iPad file directories';

  @override
  String get shareApp => 'Share';

  @override
  String get shareAppSubtitle => 'Share this app with friends';

  @override
  String get rateApp => 'Rate';

  @override
  String get rateAppSubtitle => 'Rate us on the App Store';

  @override
  String get aboutAppSubtitle => 'App version, policies and more';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get termsOfService => 'Terms of Service';

  @override
  String get appDescription =>
      'A beautiful document hub for PDFs, Word, Excel, and PowerPoint files with advanced search, sorting, and PDF creation capabilities.';

  @override
  String get authorizedDirectories => 'Manage Authorized Folders';

  @override
  String get addDirectory => 'Add Directory';

  @override
  String get removeDirectory => 'Remove';

  @override
  String get noAuthorizedDirectories => 'No authorized directories';

  @override
  String get noAuthorizedDirectoriesSubtitle =>
      'Add directories to access files from those locations';

  @override
  String get directoryAdded => 'Directory authorized successfully';

  @override
  String get directoryRemoved => 'Directory authorization removed';

  @override
  String get cannotAddDirectory => 'Cannot add directory, please try again';

  @override
  String appCanAccessDirectories(String appName) {
    return '$appName can access the following directories:';
  }

  @override
  String filesCount(int count) {
    return '$count files';
  }

  @override
  String directorySize(String size) {
    return 'Size: $size';
  }

  @override
  String get cancelAuthorization => 'Cancel Authorization';

  @override
  String confirmCancelAuthorization(String appName, String directoryName) {
    return 'Do you really want to cancel the access authorization for $appName\'s $directoryName? After cancellation, files in this directory will no longer be visible.';
  }

  @override
  String get noKeepIt => 'No, keep it';

  @override
  String get yesRemoveFolder => 'Yes, I don\'t need this folder anymore';

  @override
  String get editName => 'Edit Name';

  @override
  String get enterCustomName => 'Enter custom name';

  @override
  String get customName => 'Custom Name';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get nameForNewFolder =>
      'For easy identification later, please give a name to the app folder you just selected';

  @override
  String defaultAppName(int number) {
    return 'App $number';
  }

  @override
  String get selectFiles => 'Select Files';

  @override
  String get authorizeFolder => 'Authorize Folder';

  @override
  String get selectingDirectory => 'Scanning directory...';

  @override
  String directorySelected(String name) {
    return 'Selected directory: $name';
  }

  @override
  String get directorySelectionOnlyMobile =>
      'Directory selection is only available on iOS/Android';

  @override
  String get cannotSelectDirectory =>
      'Cannot select directory, please check permissions';

  @override
  String loadedFiles(int count) {
    return 'Scanned $count files';
  }

  @override
  String get cannotLoadDirectoryFiles => 'Cannot load directory files';

  @override
  String get iosPermissionMessage =>
      'Due to iPhone permission restrictions, you need to authorize access to the folders containing your PDF, Word, Excel, and PPT files first.';

  @override
  String get noFilesFoundMessage =>
      'No files of this type found.\nPlease select other types from above, or tap the \'+\' button to add more files.';

  @override
  String get noFilesFoundGeneral => 'No files found, pull down to scan again.';

  @override
  String get importFilesFromPhone => 'Import files from phone';

  @override
  String get selectFolder => 'Add Authorized Folder';

  @override
  String get selectFolderSubtitle =>
      'Grant folder access to scan and display files';

  @override
  String get photoToPdf => 'Photo to PDF';

  @override
  String get photoToPdfSubtitle =>
      'Use camera to take multiple photos and merge into PDF';

  @override
  String get mergeImagesToPdf => 'Merge Images to PDF';

  @override
  String get mergeImagesToPdfSubtitle => 'Merge album photos to PDF';

  @override
  String get convert => 'Convert';

  @override
  String get pdfSavedSuccessfully => 'PDF saved successfully';

  @override
  String get pdfSaveFailed => 'Failed to save PDF';

  @override
  String get undo => 'Undo';

  @override
  String get deleteImage => 'Delete Image';

  @override
  String get confirmDeleteImage =>
      'Are you sure you want to delete this image?';

  @override
  String get delete => 'Delete';

  @override
  String get reorderImage => 'Reorder images';

  @override
  String get takePicture => 'Take picture';

  @override
  String addImagesFromGallery(int count) {
    return 'Add $count images from gallery';
  }

  @override
  String get cropImage => 'Crop Image';

  @override
  String confirmUndo(String action) {
    return 'Undo: $action. This will revert your last action. Continue?';
  }

  @override
  String get cropPlaceholderMessage =>
      'Crop functionality will be implemented here';

  @override
  String get cropFeatureComingSoon => 'Advanced crop features coming soon!';

  @override
  String get addMoreImages => 'Add more images';

  @override
  String get takePhoto => 'Take Photo';

  @override
  String get importFromAlbum => 'Import from Album';

  @override
  String get sort => 'Sort';

  @override
  String get nameSort => 'Name';

  @override
  String get lastModified => 'Last Modified';

  @override
  String get sizeSort => 'Size';

  @override
  String get descending => 'Descending';

  @override
  String get apply => 'Apply';

  @override
  String get noRecentFiles => 'No recent files';

  @override
  String get noFavoriteFiles => 'You haven\'t favorited any files yet';

  @override
  String get noFavoriteFilesHint =>
      'You can favorite files by tapping the ☆ star icon next to files on the documents page';

  @override
  String get fileNotFound => 'File not found';

  @override
  String get weLikeYouToo => 'We like you too!';

  @override
  String get thankYouForFeedback => 'Thank for your feedback.';

  @override
  String get theBestWeCanGet => 'The best we can get :)';

  @override
  String get maybeLater => 'Maybe later';

  @override
  String get rateNow => 'Rate';

  @override
  String get discardChangesTitle => 'Discard changes?';

  @override
  String get discardChangesContent =>
      'You have unsaved changes. If you exit now, they will be lost.';

  @override
  String get discard => 'Discard';

  @override
  String get keepEditing => 'Keep Editing';
}
