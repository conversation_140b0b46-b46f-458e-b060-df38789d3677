// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Portuguese (`pt`).
class AppLocalizationsPt extends AppLocalizations {
  AppLocalizationsPt([String locale = 'pt']) : super(locale);

  @override
  String get appName => 'Central de Documentos';

  @override
  String get documents => 'Documentos';

  @override
  String get recent => 'Recentes';

  @override
  String get favorite => 'Favoritos';

  @override
  String get settings => 'Configurações';

  @override
  String get addFiles => 'Adicionar Arquivos';

  @override
  String get addFilesSubtitle =>
      'Escolha mais arquivos do seu dispositivo para gerenciar';

  @override
  String get followDocumentTheme =>
      'Seguir esquema de cores do tipo de documento';

  @override
  String get followDocumentThemeSubtitle => 'Estilos PDF/Word/Excel/PPT';

  @override
  String get languageSettings => 'Idioma UI';

  @override
  String get aboutApp => 'Sobre';

  @override
  String get version => 'Versão';

  @override
  String get appVersion => '1.0.0';

  @override
  String get copyright => '© 2025';

  @override
  String filesAdded(int count) {
    return 'Adicionados $count arquivos';
  }

  @override
  String get cannotSelectFiles =>
      'Não é possível selecionar arquivos, verifique as permissões';

  @override
  String get selectLanguage => 'Selecionar Idioma';

  @override
  String get systemLanguage => 'Idioma do Sistema';

  @override
  String get english => 'English';

  @override
  String get spanish => 'Español';

  @override
  String get portuguese => 'Português';

  @override
  String get french => 'Français';

  @override
  String get german => 'Deutsch';

  @override
  String get chineseSimplified => '简体中文';

  @override
  String get chineseTraditional => '繁體中文';

  @override
  String get japanese => '日本語';

  @override
  String get korean => '한국어';

  @override
  String currentLanguage(String language) {
    return 'Atual: $language';
  }

  @override
  String get selectAuthorizedDirectory => 'Gerenciar Diretório Autorizado';

  @override
  String get selectAuthorizedDirectorySubtitle =>
      'Autorizar acesso aos diretórios do iPhone/iPad';

  @override
  String get shareApp => 'Compartilhar';

  @override
  String get shareAppSubtitle => 'Compartilhe este app com amigos';

  @override
  String get rateApp => 'Avaliar';

  @override
  String get rateAppSubtitle => 'Avalie-nos na App Store';

  @override
  String get aboutAppSubtitle => 'Versão do app, políticas e mais';

  @override
  String get privacyPolicy => 'Política de Privacidade';

  @override
  String get termsOfService => 'Termos de Serviço';

  @override
  String get appDescription =>
      'Um belo centro de documentos para arquivos PDF, Word, Excel e PowerPoint com busca avançada, classificação e recursos de criação de PDF.';

  @override
  String get authorizedDirectories => 'Gerenciar Diretórios Autorizados';

  @override
  String get addDirectory => 'Adicionar Diretório';

  @override
  String get removeDirectory => 'Remover';

  @override
  String get noAuthorizedDirectories => 'Nenhum diretório autorizado';

  @override
  String get noAuthorizedDirectoriesSubtitle =>
      'Adicione diretórios para acessar arquivos desses locais';

  @override
  String get directoryAdded => 'Diretório autorizado com sucesso';

  @override
  String get directoryRemoved => 'Autorização de diretório removida';

  @override
  String get cannotAddDirectory =>
      'Não é possível adicionar diretório, tente novamente';

  @override
  String appCanAccessDirectories(String appName) {
    return '$appName pode acessar os seguintes diretórios:';
  }

  @override
  String filesCount(int count) {
    return '$count arquivos';
  }

  @override
  String directorySize(String size) {
    return 'Tamanho: $size';
  }

  @override
  String get cancelAuthorization => 'Cancelar Autorização';

  @override
  String confirmCancelAuthorization(String appName, String directoryName) {
    return 'Deseja realmente cancelar a autorização de acesso para a $directoryName do $appName? Após o cancelamento, os arquivos neste diretório não serão mais visíveis.';
  }

  @override
  String get noKeepIt => 'Não, manter';

  @override
  String get yesRemoveFolder => 'Sim, não preciso mais desta pasta';

  @override
  String get editName => 'Editar Nome';

  @override
  String get enterCustomName => 'Digite um nome personalizado';

  @override
  String get customName => 'Nome Personalizado';

  @override
  String get save => 'Salvar';

  @override
  String get cancel => 'Cancelar';

  @override
  String get nameForNewFolder =>
      'Para facilitar a identificação posteriormente, dê um nome à pasta do aplicativo que você acabou de selecionar';

  @override
  String defaultAppName(int number) {
    return 'App $number';
  }

  @override
  String get selectFiles => 'Selecionar Arquivos';

  @override
  String get authorizeFolder => 'Autorizar Pasta';

  @override
  String get selectingDirectory => 'Escaneando diretório...';

  @override
  String directorySelected(String name) {
    return 'Diretório selecionado: $name';
  }

  @override
  String get directorySelectionOnlyMobile =>
      'A seleção de diretório só está disponível no iOS/Android';

  @override
  String get cannotSelectDirectory =>
      'Não é possível selecionar diretório, verifique as permissões';

  @override
  String loadedFiles(int count) {
    return '$count arquivos escaneados';
  }

  @override
  String get cannotLoadDirectoryFiles =>
      'Não é possível carregar arquivos do diretório';

  @override
  String get iosPermissionMessage =>
      'Devido às restrições de permissão do iPhone, você precisa primeiro autorizar acesso às pastas que contêm seus arquivos PDF, Word, Excel e PPT.';

  @override
  String get noFilesFoundMessage =>
      'Nenhum arquivo deste tipo encontrado.\nPor favor, selecione outros tipos acima, ou toque no botão \'+\' para adicionar mais arquivos.';

  @override
  String get noFilesFoundGeneral =>
      'Nenhum arquivo encontrado, puxe para baixo para escanear novamente.';

  @override
  String get importFilesFromPhone => 'Importar arquivos do telefone';

  @override
  String get selectFolder => 'Adicionar Pasta Autorizada';

  @override
  String get selectFolderSubtitle =>
      'Conceder acesso à pasta para escanear e exibir arquivos';

  @override
  String get photoToPdf => 'Foto para PDF';

  @override
  String get photoToPdfSubtitle =>
      'Use a câmera do celular para tirar múltiplas fotos e mesclá-las automaticamente em PDF';

  @override
  String get mergeImagesToPdf => 'Mesclar Imagens em PDF';

  @override
  String get mergeImagesToPdfSubtitle => 'Mesclar Fotos do Álbum em PDF';

  @override
  String get convert => 'Converter';

  @override
  String get pdfSavedSuccessfully => 'PDF salvo com sucesso';

  @override
  String get pdfSaveFailed => 'Falha ao salvar PDF';

  @override
  String get undo => 'Desfazer';

  @override
  String get deleteImage => 'Excluir imagem';

  @override
  String get confirmDeleteImage =>
      'Tem certeza de que deseja excluir esta imagem?';

  @override
  String get delete => 'Excluir';

  @override
  String get reorderImage => 'Reordenar imagens';

  @override
  String get takePicture => 'Tirar foto';

  @override
  String addImagesFromGallery(int count) {
    return 'Adicionar $count imagens da galeria';
  }

  @override
  String get cropImage => 'Cortar imagem';

  @override
  String confirmUndo(String action) {
    return 'Desfazer: $action. Isso reverterá sua última ação. Continuar?';
  }

  @override
  String get cropPlaceholderMessage =>
      'Funcionalidade de corte será implementada aqui';

  @override
  String get cropFeatureComingSoon => 'Recursos avançados de corte em breve!';

  @override
  String get addMoreImages => 'Adicionar mais imagens';

  @override
  String get takePhoto => 'Tirar foto';

  @override
  String get importFromAlbum => 'Importar do álbum';

  @override
  String get sort => 'Ordenar';

  @override
  String get nameSort => 'Nome';

  @override
  String get lastModified => 'Última Modificação';

  @override
  String get sizeSort => 'Tamanho';

  @override
  String get descending => 'Decrescente';

  @override
  String get apply => 'Aplicar';

  @override
  String get noRecentFiles => 'Nenhum arquivo recente';

  @override
  String get noFavoriteFiles => 'Você ainda não favoritou nenhum arquivo';

  @override
  String get noFavoriteFilesHint =>
      'Você pode favoritar arquivos tocando no ícone ☆ estrela ao lado dos arquivos na página de documentos';

  @override
  String get fileNotFound => 'Arquivo não encontrado';

  @override
  String get weLikeYouToo => 'Nós também gostamos de você!';

  @override
  String get thankYouForFeedback => 'Obrigado pelo seu feedback.';

  @override
  String get theBestWeCanGet => 'O melhor que podemos conseguir :)';

  @override
  String get maybeLater => 'Talvez mais tarde';

  @override
  String get rateNow => 'Avaliar';

  @override
  String get discardChangesTitle => 'Discard changes?';

  @override
  String get discardChangesContent =>
      'You have unsaved changes. If you exit now, they will be lost.';

  @override
  String get discard => 'Discard';

  @override
  String get keepEditing => 'Keep Editing';
}
