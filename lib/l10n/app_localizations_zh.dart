// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appName => '全能PDF编辑器';

  @override
  String get documents => '文档';

  @override
  String get recent => '最近';

  @override
  String get favorite => '收藏';

  @override
  String get settings => '设置';

  @override
  String get addFiles => '添加文件';

  @override
  String get addFilesSubtitle => '从设备中选择更多要管理的文件';

  @override
  String get followDocumentTheme => '跟随文档类型切换配色';

  @override
  String get followDocumentThemeSubtitle => 'PDF/Word/Excel/PPT 风格';

  @override
  String get languageSettings => '界面语言';

  @override
  String get aboutApp => '关于';

  @override
  String get version => '版本';

  @override
  String get appVersion => '1.0.0';

  @override
  String get copyright => '© 2025';

  @override
  String filesAdded(int count) {
    return '添加了 $count 个文件';
  }

  @override
  String get cannotSelectFiles => '无法选择文件，请检查权限';

  @override
  String get selectLanguage => '选择语言';

  @override
  String get systemLanguage => '系统语言';

  @override
  String get english => 'English';

  @override
  String get spanish => 'Español';

  @override
  String get portuguese => 'Português';

  @override
  String get french => 'Français';

  @override
  String get german => 'Deutsch';

  @override
  String get chineseSimplified => '简体中文';

  @override
  String get chineseTraditional => '繁體中文';

  @override
  String get japanese => '日本語';

  @override
  String get korean => '한국어';

  @override
  String currentLanguage(String language) {
    return '当前: $language';
  }

  @override
  String get selectAuthorizedDirectory => '管理授权文件夹';

  @override
  String get selectAuthorizedDirectorySubtitle => '授权访问iPhone的文件目录';

  @override
  String get shareApp => '分享';

  @override
  String get shareAppSubtitle => '与朋友分享此应用';

  @override
  String get rateApp => '评分';

  @override
  String get rateAppSubtitle => '在应用商店为我们评分';

  @override
  String get aboutAppSubtitle => '本App的版本、政策等';

  @override
  String get privacyPolicy => '隐私政策';

  @override
  String get termsOfService => '服务条款';

  @override
  String get appDescription =>
      '全能PDF编辑工具，支持从Word、Excel、PowerPoint和图片创建PDF，编辑PDF文件，为PDF加签名、做笔记等。';

  @override
  String get authorizedDirectories => '管理授权文件夹';

  @override
  String get addDirectory => '添加目录';

  @override
  String get removeDirectory => '移除';

  @override
  String get noAuthorizedDirectories => '暂无授权目录';

  @override
  String get noAuthorizedDirectoriesSubtitle => '添加目录以访问这些位置的文件';

  @override
  String get directoryAdded => '目录授权成功';

  @override
  String get directoryRemoved => '已移除目录授权';

  @override
  String get cannotAddDirectory => '无法添加目录，请重试';

  @override
  String appCanAccessDirectories(String appName) {
    return '$appName已经可以访问以下目录：';
  }

  @override
  String filesCount(int count) {
    return '$count个文件';
  }

  @override
  String directorySize(String size) {
    return '大小：$size';
  }

  @override
  String get cancelAuthorization => '取消授权';

  @override
  String confirmCancelAuthorization(String appName, String directoryName) {
    return '您真的要取消对 $appName 的 $directoryName 的访问授权吗？取消以后这个目录下的文件就看不到了。';
  }

  @override
  String get noKeepIt => '不，保留它';

  @override
  String get yesRemoveFolder => '是的，我不用这个文件夹了';

  @override
  String get editName => '编辑名称';

  @override
  String get enterCustomName => '输入自定义名称';

  @override
  String get customName => '自定义名称';

  @override
  String get save => '保存';

  @override
  String get cancel => '取消';

  @override
  String get nameForNewFolder => '为了方便日后查看，请您为刚选的应用文件夹起个名字';

  @override
  String defaultAppName(int number) {
    return '应用 $number';
  }

  @override
  String get selectFiles => '选择文件';

  @override
  String get authorizeFolder => '授权文件夹';

  @override
  String get selectingDirectory => '正在扫描目录...';

  @override
  String directorySelected(String name) {
    return '已选择目录: $name';
  }

  @override
  String get directorySelectionOnlyMobile => '目录选择功能仅在 iOS/Android 上可用';

  @override
  String get cannotSelectDirectory => '无法选择目录，请检查权限';

  @override
  String loadedFiles(int count) {
    return '已扫描 $count 个文件';
  }

  @override
  String get cannotLoadDirectoryFiles => '无法加载目录文件';

  @override
  String get iosPermissionMessage =>
      '由于iPhone的权限限制，您需要先授权访问您的PDF、Word、Excel、PPT文件所在的文件夹。';

  @override
  String get noFilesFoundMessage => '未找到该类型的文件。\n请从上方选择其他类型，或点击\'+\'按钮添加更多文件。';

  @override
  String get noFilesFoundGeneral => '未找到任何文件，下拉以重新扫描。';

  @override
  String get importFilesFromPhone => '导入手机上的文件';

  @override
  String get selectFolder => '添加授权文件夹';

  @override
  String get selectFolderSubtitle => '授权文件夹访问权限以扫描和显示文件';

  @override
  String get photoToPdf => '拍照转 PDF';

  @override
  String get photoToPdfSubtitle => '使用手机相机拍摄多张照片并自动合并成PDF';

  @override
  String get mergeImagesToPdf => '合并图片成PDF';

  @override
  String get mergeImagesToPdfSubtitle => '合并相册图片成PDF';

  @override
  String get convert => '转换';

  @override
  String get pdfSavedSuccessfully => 'PDF保存成功';

  @override
  String get pdfSaveFailed => 'PDF保存失败';

  @override
  String get undo => '撤销';

  @override
  String get deleteImage => '删除图片';

  @override
  String get confirmDeleteImage => '确定要删除这张图片吗？';

  @override
  String get delete => '删除';

  @override
  String get reorderImage => '调整图片顺序';

  @override
  String get takePicture => '拍照';

  @override
  String addImagesFromGallery(int count) {
    return '从相册添加$count张图片';
  }

  @override
  String get cropImage => '裁剪图片';

  @override
  String confirmUndo(String action) {
    return '撤销：$action。这将回退您的上一步操作，是否继续？';
  }

  @override
  String get cropPlaceholderMessage => '裁剪功能将在此处实现';

  @override
  String get cropFeatureComingSoon => '高级裁剪功能即将推出！';

  @override
  String get addMoreImages => '添加更多图片';

  @override
  String get takePhoto => '拍照';

  @override
  String get importFromAlbum => '从相册导入';

  @override
  String get sort => '排序';

  @override
  String get nameSort => '名称';

  @override
  String get lastModified => '最后修改时间';

  @override
  String get sizeSort => '大小';

  @override
  String get descending => '降序';

  @override
  String get apply => '应用';

  @override
  String get noRecentFiles => '暂无最近打开文件';

  @override
  String get noFavoriteFiles => '您还没有收藏过文件';

  @override
  String get noFavoriteFilesHint => '您可以在文档页面显示的文件右侧点击☆星号来收藏文件';

  @override
  String get fileNotFound => '文件不存在';

  @override
  String get weLikeYouToo => '我们也很喜欢您！';

  @override
  String get thankYouForFeedback => '感谢您的反馈。';

  @override
  String get theBestWeCanGet => '这是我们能得到的最好评价了 :)';

  @override
  String get maybeLater => '稍后再说';

  @override
  String get rateNow => '去评分';

  @override
  String get discardChangesTitle => '要放弃更改吗？';

  @override
  String get discardChangesContent => '您有未保存的更改。如果现在退出，这些更改将会丢失。';

  @override
  String get discard => '放弃';

  @override
  String get keepEditing => '继续编辑';
}

/// The translations for Chinese, as used in Taiwan (`zh_TW`).
class AppLocalizationsZhTw extends AppLocalizationsZh {
  AppLocalizationsZhTw() : super('zh_TW');

  @override
  String get appName => '全能PDF編輯器';

  @override
  String get documents => '文件';

  @override
  String get recent => '最近';

  @override
  String get favorite => '收藏';

  @override
  String get settings => '設定';

  @override
  String get addFiles => '加入檔案';

  @override
  String get addFilesSubtitle => '從裝置中選擇更多要管理的檔案';

  @override
  String get followDocumentTheme => '跟隨文件類型切換配色';

  @override
  String get followDocumentThemeSubtitle => 'PDF/Word/Excel/PPT 風格';

  @override
  String get languageSettings => '介面語言';

  @override
  String get aboutApp => '關於';

  @override
  String get version => '版本';

  @override
  String get appVersion => '1.0.0';

  @override
  String get copyright => '© 2025';

  @override
  String filesAdded(int count) {
    return '加入了 $count 個檔案';
  }

  @override
  String get cannotSelectFiles => '無法選擇檔案，請檢查權限';

  @override
  String get selectLanguage => '選擇語言';

  @override
  String get systemLanguage => '系統語言';

  @override
  String get english => 'English';

  @override
  String get spanish => 'Español';

  @override
  String get portuguese => 'Português';

  @override
  String get french => 'Français';

  @override
  String get german => 'Deutsch';

  @override
  String get chineseSimplified => '简体中文';

  @override
  String get chineseTraditional => '繁體中文';

  @override
  String get japanese => '日本語';

  @override
  String get korean => '한국어';

  @override
  String currentLanguage(String language) {
    return '目前: $language';
  }

  @override
  String get selectAuthorizedDirectory => '管理授權目錄';

  @override
  String get selectAuthorizedDirectorySubtitle => '授權存取iPhone/iPad的檔案目錄';

  @override
  String get shareApp => '分享';

  @override
  String get shareAppSubtitle => '與朋友分享此應用程式';

  @override
  String get rateApp => '評分';

  @override
  String get rateAppSubtitle => '在 App Store 為我們評分';

  @override
  String get aboutAppSubtitle => '本 App 的版本、政策等';

  @override
  String get privacyPolicy => '隱私政策';

  @override
  String get termsOfService => '服務條款';

  @override
  String get appDescription =>
      '全能PDF編輯工具，支援從Word、Excel、PowerPoint和圖片建立PDF，編輯PDF文件，為PDF加上簽名、做筆記等。';

  @override
  String get authorizedDirectories => '管理授權目錄';

  @override
  String get addDirectory => '新增目錄';

  @override
  String get removeDirectory => '移除';

  @override
  String get noAuthorizedDirectories => '暫無授權目錄';

  @override
  String get noAuthorizedDirectoriesSubtitle => '新增目錄以存取這些位置的檔案';

  @override
  String get directoryAdded => '目錄授權成功';

  @override
  String get directoryRemoved => '已移除目錄授權';

  @override
  String get cannotAddDirectory => '無法新增目錄，請重試';

  @override
  String appCanAccessDirectories(String appName) {
    return '$appName已經可以存取以下目錄：';
  }

  @override
  String filesCount(int count) {
    return '$count個檔案';
  }

  @override
  String directorySize(String size) {
    return '大小：$size';
  }

  @override
  String get cancelAuthorization => '取消授權';

  @override
  String confirmCancelAuthorization(String appName, String directoryName) {
    return '您真的要取消對 $appName 的 $directoryName 的存取授權嗎？取消後，此目錄中的檔案將不再可見。';
  }

  @override
  String get noKeepIt => '不，保留它';

  @override
  String get yesRemoveFolder => '是的，我不再需要此資料夾';

  @override
  String get editName => '編輯名稱';

  @override
  String get enterCustomName => '輸入自訂名稱';

  @override
  String get customName => '自訂名稱';

  @override
  String get save => '儲存';

  @override
  String get cancel => '取消';

  @override
  String get nameForNewFolder => '為方便日後查看，請您為剛選的應用資料夾起個名字';

  @override
  String defaultAppName(int number) {
    return '應用 $number';
  }

  @override
  String get selectFiles => '選擇檔案';

  @override
  String get authorizeFolder => '授權資料夾';

  @override
  String get selectingDirectory => '正在掃描目錄...';

  @override
  String directorySelected(String name) {
    return '已選擇目錄：$name';
  }

  @override
  String get directorySelectionOnlyMobile => '目錄選擇僅在 iOS/Android 上可用';

  @override
  String get cannotSelectDirectory => '無法選擇目錄，請檢查權限';

  @override
  String loadedFiles(int count) {
    return '已掃描 $count 個檔案';
  }

  @override
  String get cannotLoadDirectoryFiles => '無法載入目錄檔案';

  @override
  String get iosPermissionMessage =>
      '由於 iPhone 權限限制，您需要先授權存取包含 PDF、Word、Excel 和 PPT 檔案的資料夾。';

  @override
  String get noFilesFoundMessage => '找不到此類型的檔案。\n請從上方選擇其他類型，或點擊\'+\'按鈕新增更多檔案。';

  @override
  String get noFilesFoundGeneral => '找不到檔案，向下拉動重新掃描。';

  @override
  String get importFilesFromPhone => '從手機匯入檔案';

  @override
  String get selectFolder => '新增授權資料夾';

  @override
  String get selectFolderSubtitle => '授權資料夾存取權限以掃描和顯示檔案';

  @override
  String get photoToPdf => '照片轉 PDF';

  @override
  String get photoToPdfSubtitle => '使用手機相機拍攝多張照片並自動合併成PDF';

  @override
  String get mergeImagesToPdf => '合併圖片成PDF';

  @override
  String get mergeImagesToPdfSubtitle => '合併相簿圖片成PDF';

  @override
  String get convert => '轉換';

  @override
  String get pdfSavedSuccessfully => 'PDF儲存成功';

  @override
  String get pdfSaveFailed => 'PDF儲存失敗';

  @override
  String get undo => '復原';

  @override
  String get deleteImage => '刪除圖片';

  @override
  String get confirmDeleteImage => '您確定要刪除這張圖片嗎？';

  @override
  String get delete => '刪除';

  @override
  String get reorderImage => '調整圖片順序';

  @override
  String get takePicture => '拍照';

  @override
  String addImagesFromGallery(int count) {
    return '從相簿新增$count張圖片';
  }

  @override
  String get cropImage => '裁剪圖片';

  @override
  String confirmUndo(String action) {
    return '復原：$action。這將復原您的上一步操作，是否繼續？';
  }

  @override
  String get cropPlaceholderMessage => '裁剪功能將在此處實現';

  @override
  String get cropFeatureComingSoon => '進階裁剪功能即將推出！';

  @override
  String get addMoreImages => '新增更多圖片';

  @override
  String get takePhoto => '拍照';

  @override
  String get importFromAlbum => '從相簿匯入';

  @override
  String get sort => '排序';

  @override
  String get nameSort => '名稱';

  @override
  String get lastModified => '最後修改';

  @override
  String get sizeSort => '大小';

  @override
  String get descending => '降序';

  @override
  String get apply => '套用';

  @override
  String get noRecentFiles => '暫無最近開啟檔案';

  @override
  String get noFavoriteFiles => '您還沒有收藏過檔案';

  @override
  String get noFavoriteFilesHint => '您可以在文件頁面顯示的檔案右側點擊☆星號來收藏檔案';

  @override
  String get fileNotFound => '檔案不存在';

  @override
  String get weLikeYouToo => '我們也很喜歡您！';

  @override
  String get thankYouForFeedback => '感謝您的回饋。';

  @override
  String get theBestWeCanGet => '這是我們能得到的最好評價了 :)';

  @override
  String get maybeLater => '稍後再說';

  @override
  String get rateNow => '去評分';
}
