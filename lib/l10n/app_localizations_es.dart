// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get appName => 'Centro de Documentos';

  @override
  String get documents => 'Documentos';

  @override
  String get recent => 'Recientes';

  @override
  String get favorite => 'Favoritos';

  @override
  String get settings => 'Configuración';

  @override
  String get addFiles => 'Agregar Archivos';

  @override
  String get addFilesSubtitle =>
      'Elige más archivos de tu dispositivo para administrar';

  @override
  String get followDocumentTheme =>
      'Seguir esquema de colores del tipo de documento';

  @override
  String get followDocumentThemeSubtitle => 'Estilos PDF/Word/Excel/PPT';

  @override
  String get languageSettings => 'Idioma UI';

  @override
  String get aboutApp => 'Acerca de';

  @override
  String get version => 'Versión';

  @override
  String get appVersion => '1.0.0';

  @override
  String get copyright => '© 2025';

  @override
  String filesAdded(int count) {
    return 'Se agregaron $count archivos';
  }

  @override
  String get cannotSelectFiles =>
      'No se pueden seleccionar archivos, verifique los permisos';

  @override
  String get selectLanguage => 'Seleccionar Idioma';

  @override
  String get systemLanguage => 'Idioma del Sistema';

  @override
  String get english => 'English';

  @override
  String get spanish => 'Español';

  @override
  String get portuguese => 'Português';

  @override
  String get french => 'Français';

  @override
  String get german => 'Deutsch';

  @override
  String get chineseSimplified => '简体中文';

  @override
  String get chineseTraditional => '繁體中文';

  @override
  String get japanese => '日本語';

  @override
  String get korean => '한국어';

  @override
  String currentLanguage(String language) {
    return 'Actual: $language';
  }

  @override
  String get selectAuthorizedDirectory => 'Administrar Directorio Autorizado';

  @override
  String get selectAuthorizedDirectorySubtitle =>
      'Autorizar acceso a directorios de iPhone/iPad';

  @override
  String get shareApp => 'Compartir';

  @override
  String get shareAppSubtitle => 'Comparte esta app con amigos';

  @override
  String get rateApp => 'Calificar';

  @override
  String get rateAppSubtitle => 'Califícanos en la App Store';

  @override
  String get aboutAppSubtitle => 'Versión de la app, políticas y más';

  @override
  String get privacyPolicy => 'Política de Privacidad';

  @override
  String get termsOfService => 'Términos de Servicio';

  @override
  String get appDescription =>
      'Un hermoso centro de documentos para archivos PDF, Word, Excel y PowerPoint con búsqueda avanzada, clasificación y capacidades de creación de PDF.';

  @override
  String get authorizedDirectories => 'Administrar Directorios Autorizados';

  @override
  String get addDirectory => 'Agregar Directorio';

  @override
  String get removeDirectory => 'Eliminar';

  @override
  String get noAuthorizedDirectories => 'Sin directorios autorizados';

  @override
  String get noAuthorizedDirectoriesSubtitle =>
      'Agrega directorios para acceder a archivos desde esas ubicaciones';

  @override
  String get directoryAdded => 'Directorio autorizado exitosamente';

  @override
  String get directoryRemoved => 'Autorización de directorio eliminada';

  @override
  String get cannotAddDirectory =>
      'No se puede agregar directorio, inténtalo de nuevo';

  @override
  String appCanAccessDirectories(String appName) {
    return '$appName puede acceder a los siguientes directorios:';
  }

  @override
  String filesCount(int count) {
    return '$count archivos';
  }

  @override
  String directorySize(String size) {
    return 'Tamaño: $size';
  }

  @override
  String get cancelAuthorization => 'Cancelar autorización';

  @override
  String confirmCancelAuthorization(String appName, String directoryName) {
    return '¿Realmente desea cancelar la autorización de acceso para $directoryName de $appName? Después de la cancelación, los archivos en este directorio ya no estarán visibles.';
  }

  @override
  String get noKeepIt => 'No, mantenerlo';

  @override
  String get yesRemoveFolder => 'Sí, ya no necesito esta carpeta';

  @override
  String get editName => 'Editar nombre';

  @override
  String get enterCustomName => 'Introducir nombre personalizado';

  @override
  String get customName => 'Nombre personalizado';

  @override
  String get save => 'Guardar';

  @override
  String get cancel => 'Cancelar';

  @override
  String get nameForNewFolder =>
      'Para una fácil identificación posterior, por favor asigne un nombre a la carpeta de la aplicación que acaba de seleccionar';

  @override
  String defaultAppName(int number) {
    return 'App $number';
  }

  @override
  String get selectFiles => 'Seleccionar Archivos';

  @override
  String get authorizeFolder => 'Autorizar Carpeta';

  @override
  String get selectingDirectory => 'Escaneando directorio...';

  @override
  String directorySelected(String name) {
    return 'Directorio seleccionado: $name';
  }

  @override
  String get directorySelectionOnlyMobile =>
      'La selección de directorio solo está disponible en iOS/Android';

  @override
  String get cannotSelectDirectory =>
      'No se puede seleccionar directorio, verifique los permisos';

  @override
  String loadedFiles(int count) {
    return '$count archivos escaneados';
  }

  @override
  String get cannotLoadDirectoryFiles =>
      'No se pueden cargar archivos del directorio';

  @override
  String get iosPermissionMessage =>
      'Debido a las restricciones de permisos del iPhone, necesitas autorizar primero el acceso a las carpetas que contienen tus archivos PDF, Word, Excel y PPT.';

  @override
  String get noFilesFoundMessage =>
      'No se encontraron archivos de este tipo.\nPor favor selecciona otros tipos de arriba, o toca el botón \'+\' para agregar más archivos.';

  @override
  String get noFilesFoundGeneral =>
      'No se encontraron archivos, desliza hacia abajo para escanear de nuevo.';

  @override
  String get importFilesFromPhone => 'Importar archivos del teléfono';

  @override
  String get selectFolder => 'Agregar Carpeta Autorizada';

  @override
  String get selectFolderSubtitle =>
      'Otorgar acceso a la carpeta para escanear y mostrar archivos';

  @override
  String get photoToPdf => 'Foto a PDF';

  @override
  String get photoToPdfSubtitle =>
      'Usa la cámara del teléfono para tomar múltiples fotos y combinarlas en PDF';

  @override
  String get mergeImagesToPdf => 'Combinar Imágenes en PDF';

  @override
  String get mergeImagesToPdfSubtitle => 'Combinar Fotos del Álbum en PDF';

  @override
  String get convert => 'Convertir';

  @override
  String get pdfSavedSuccessfully => 'PDF guardado exitosamente';

  @override
  String get pdfSaveFailed => 'Error al guardar PDF';

  @override
  String get undo => 'Deshacer';

  @override
  String get deleteImage => 'Eliminar imagen';

  @override
  String get confirmDeleteImage =>
      '¿Está seguro de que desea eliminar esta imagen?';

  @override
  String get delete => 'Eliminar';

  @override
  String get reorderImage => 'Reordenar imágenes';

  @override
  String get takePicture => 'Tomar foto';

  @override
  String addImagesFromGallery(int count) {
    return 'Agregar $count imágenes de la galería';
  }

  @override
  String get cropImage => 'Recortar imagen';

  @override
  String confirmUndo(String action) {
    return 'Deshacer: $action. Esto revertirá su última acción. ¿Continuar?';
  }

  @override
  String get cropPlaceholderMessage =>
      'La funcionalidad de recorte se implementará aquí';

  @override
  String get cropFeatureComingSoon =>
      '¡Características avanzadas de recorte próximamente!';

  @override
  String get addMoreImages => 'Agregar más imágenes';

  @override
  String get takePhoto => 'Tomar foto';

  @override
  String get importFromAlbum => 'Importar del álbum';

  @override
  String get sort => 'Ordenar';

  @override
  String get nameSort => 'Nombre';

  @override
  String get lastModified => 'Última Modificación';

  @override
  String get sizeSort => 'Tamaño';

  @override
  String get descending => 'Descendente';

  @override
  String get apply => 'Aplicar';

  @override
  String get noRecentFiles => 'No hay archivos recientes';

  @override
  String get noFavoriteFiles => 'Aún no has marcado archivos como favoritos';

  @override
  String get noFavoriteFilesHint =>
      'Puedes marcar archivos como favoritos tocando el ícono ☆ estrella junto a los archivos en la página de documentos';

  @override
  String get fileNotFound => 'Archivo no encontrado';

  @override
  String get weLikeYouToo => '¡A nosotros también nos gustas!';

  @override
  String get thankYouForFeedback => 'Gracias por tu comentario.';

  @override
  String get theBestWeCanGet => 'Lo mejor que podemos conseguir :)';

  @override
  String get maybeLater => 'Tal vez más tarde';

  @override
  String get rateNow => 'Calificar';

  @override
  String get discardChangesTitle => 'Discard changes?';

  @override
  String get discardChangesContent =>
      'You have unsaved changes. If you exit now, they will be lost.';

  @override
  String get discard => 'Discard';

  @override
  String get keepEditing => 'Keep Editing';
}
