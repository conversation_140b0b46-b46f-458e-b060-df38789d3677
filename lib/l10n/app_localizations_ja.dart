// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Japanese (`ja`).
class AppLocalizationsJa extends AppLocalizations {
  AppLocalizationsJa([String locale = 'ja']) : super(locale);

  @override
  String get appName => 'オールドキュメントハブ';

  @override
  String get documents => 'ドキュメント';

  @override
  String get recent => '最近';

  @override
  String get favorite => 'お気に入り';

  @override
  String get settings => '設定';

  @override
  String get addFiles => 'ファイルを追加';

  @override
  String get addFilesSubtitle => '管理するためにデバイスからより多くのファイルを選択';

  @override
  String get followDocumentTheme => 'ドキュメントタイプのカラースキームに従う';

  @override
  String get followDocumentThemeSubtitle => 'PDF/Word/Excel/PPTスタイル';

  @override
  String get languageSettings => 'UI言語';

  @override
  String get aboutApp => 'について';

  @override
  String get version => 'バージョン';

  @override
  String get appVersion => '1.0.0';

  @override
  String get copyright => '© 2025';

  @override
  String filesAdded(int count) {
    return '$count個のファイルを追加しました';
  }

  @override
  String get cannotSelectFiles => 'ファイルを選択できません。権限を確認してください';

  @override
  String get selectLanguage => '言語を選択';

  @override
  String get systemLanguage => 'システム言語';

  @override
  String get english => 'English';

  @override
  String get spanish => 'Español';

  @override
  String get portuguese => 'Português';

  @override
  String get french => 'Français';

  @override
  String get german => 'Deutsch';

  @override
  String get chineseSimplified => '简体中文';

  @override
  String get chineseTraditional => '繁體中文';

  @override
  String get japanese => '日本語';

  @override
  String get korean => '한국어';

  @override
  String currentLanguage(String language) {
    return '現在: $language';
  }

  @override
  String get selectAuthorizedDirectory => '認証されたディレクトリを管理';

  @override
  String get selectAuthorizedDirectorySubtitle =>
      'iPhone/iPadのファイルディレクトリへのアクセスを許可';

  @override
  String get shareApp => '共有';

  @override
  String get shareAppSubtitle => '友達とこのアプリを共有';

  @override
  String get rateApp => '評価';

  @override
  String get rateAppSubtitle => 'App Storeで評価してください';

  @override
  String get aboutAppSubtitle => '本アプリのバージョン、ポリシーなど';

  @override
  String get privacyPolicy => 'プライバシーポリシー';

  @override
  String get termsOfService => '利用規約';

  @override
  String get appDescription =>
      'PDF、Word、Excel、PowerPointファイルの高度な検索、ソート、PDF作成機能を備えた美しいドキュメントハブ。';

  @override
  String get authorizedDirectories => '認証されたディレクトリを管理';

  @override
  String get addDirectory => 'ディレクトリを追加';

  @override
  String get removeDirectory => '削除';

  @override
  String get noAuthorizedDirectories => '認証されたディレクトリがありません';

  @override
  String get noAuthorizedDirectoriesSubtitle =>
      'これらの場所からファイルにアクセスするためにディレクトリを追加';

  @override
  String get directoryAdded => 'ディレクトリが正常に認証されました';

  @override
  String get directoryRemoved => 'ディレクトリの認証が削除されました';

  @override
  String get cannotAddDirectory => 'ディレクトリを追加できません。再試行してください';

  @override
  String appCanAccessDirectories(String appName) {
    return '$appNameは以下のディレクトリにアクセスできます：';
  }

  @override
  String filesCount(int count) {
    return '$count個のファイル';
  }

  @override
  String directorySize(String size) {
    return 'サイズ: $size';
  }

  @override
  String get cancelAuthorization => '承認のキャンセル';

  @override
  String confirmCancelAuthorization(String appName, String directoryName) {
    return '$appNameの$directoryNameへのアクセス承認を本当にキャンセルしますか？キャンセル後、このディレクトリ内のファイルは表示されなくなります。';
  }

  @override
  String get noKeepIt => 'いいえ、保持します';

  @override
  String get yesRemoveFolder => 'はい、このフォルダはもう必要ありません';

  @override
  String get editName => '名前を編集';

  @override
  String get enterCustomName => 'カスタム名を入力';

  @override
  String get customName => 'カスタム名';

  @override
  String get save => '保存';

  @override
  String get cancel => 'キャンセル';

  @override
  String get nameForNewFolder => '後で簡単に識別できるように、選択したアプリフォルダに名前を付けてください';

  @override
  String defaultAppName(int number) {
    return 'アプリ $number';
  }

  @override
  String get selectFiles => 'ファイルを選択';

  @override
  String get authorizeFolder => 'フォルダを認証';

  @override
  String get selectingDirectory => 'ディレクトリをスキャン中...';

  @override
  String directorySelected(String name) {
    return '選択されたディレクトリ: $name';
  }

  @override
  String get directorySelectionOnlyMobile => 'ディレクトリ選択はiOS/Androidでのみ利用可能です';

  @override
  String get cannotSelectDirectory => 'ディレクトリを選択できません。権限を確認してください';

  @override
  String loadedFiles(int count) {
    return '$count個のファイルをスキャンしました';
  }

  @override
  String get cannotLoadDirectoryFiles => 'ディレクトリファイルを読み込めません';

  @override
  String get iosPermissionMessage =>
      'iPhoneの権限制限により、まずPDF、Word、Excel、PPTファイルを含むフォルダへのアクセスを認証する必要があります。';

  @override
  String get noFilesFoundMessage =>
      'この種類のファイルが見つかりませんでした。\n上記から他の種類を選択するか、\'+\'ボタンをタップしてより多くのファイルを追加してください。';

  @override
  String get noFilesFoundGeneral => 'ファイルが見つかりません。下にプルして再スキャンしてください。';

  @override
  String get importFilesFromPhone => '電話からファイルをインポート';

  @override
  String get selectFolder => '認証フォルダを追加';

  @override
  String get selectFolderSubtitle => 'フォルダアクセスを許可してファイルをスキャン・表示';

  @override
  String get photoToPdf => '写真をPDFに';

  @override
  String get photoToPdfSubtitle => 'スマホのカメラで複数の写真を撮影し、自動でPDFに結合';

  @override
  String get mergeImagesToPdf => '画像をPDFに結合';

  @override
  String get mergeImagesToPdfSubtitle => 'アルバム画像をPDFに合成';

  @override
  String get convert => '変換';

  @override
  String get pdfSavedSuccessfully => 'PDFの保存が成功しました';

  @override
  String get pdfSaveFailed => 'PDFの保存に失敗しました';

  @override
  String get undo => '元に戻す';

  @override
  String get deleteImage => '画像を削除';

  @override
  String get confirmDeleteImage => 'この画像を削除しますか？';

  @override
  String get delete => '削除';

  @override
  String get reorderImage => '画像の順序変更';

  @override
  String get takePicture => '写真を撮る';

  @override
  String addImagesFromGallery(int count) {
    return 'ギャラリーから$count枚の画像を追加';
  }

  @override
  String get cropImage => '画像をトリミング';

  @override
  String confirmUndo(String action) {
    return '元に戻す：$action。最後の操作を元に戻します。続けますか？';
  }

  @override
  String get cropPlaceholderMessage => 'トリミング機能はここに実装されます';

  @override
  String get cropFeatureComingSoon => '高度なトリミング機能が近日公開！';

  @override
  String get addMoreImages => 'さらに画像を追加';

  @override
  String get takePhoto => '写真を撮る';

  @override
  String get importFromAlbum => 'アルバムからインポート';

  @override
  String get sort => 'ソート';

  @override
  String get nameSort => '名前';

  @override
  String get lastModified => '最終更新日';

  @override
  String get sizeSort => 'サイズ';

  @override
  String get descending => '降順';

  @override
  String get apply => '適用';

  @override
  String get noRecentFiles => '最近のファイルはありません';

  @override
  String get noFavoriteFiles => 'まだファイルをお気に入りに追加していません';

  @override
  String get noFavoriteFilesHint =>
      'ドキュメントページでファイルの横にある☆星アイコンをタップしてお気に入りに追加できます';

  @override
  String get fileNotFound => 'ファイルが見つかりません';

  @override
  String get weLikeYouToo => '私たちもあなたが大好きです！';

  @override
  String get thankYouForFeedback => 'フィードバックをありがとうございます。';

  @override
  String get theBestWeCanGet => '最高の評価です :)';

  @override
  String get maybeLater => '後で';

  @override
  String get rateNow => '評価する';

  @override
  String get discardChangesTitle => 'Discard changes?';

  @override
  String get discardChangesContent =>
      'You have unsaved changes. If you exit now, they will be lost.';

  @override
  String get discard => 'Discard';

  @override
  String get keepEditing => 'Keep Editing';
}
