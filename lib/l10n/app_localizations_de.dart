// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for German (`de`).
class AppLocalizationsDe extends AppLocalizations {
  AppLocalizationsDe([String locale = 'de']) : super(locale);

  @override
  String get appName => 'Dokumenten-Hub';

  @override
  String get documents => 'Dokumente';

  @override
  String get recent => 'Kürzlich';

  @override
  String get favorite => 'Favoriten';

  @override
  String get settings => 'Einstellungen';

  @override
  String get addFiles => 'Dateien hinzufügen';

  @override
  String get addFilesSubtitle =>
      'Wählen Sie weitere Dateien von Ihrem Gerät zur Verwaltung';

  @override
  String get followDocumentTheme => 'Farbschema nach Dokumenttyp folgen';

  @override
  String get followDocumentThemeSubtitle => 'PDF/Word/Excel/PPT-Stile';

  @override
  String get languageSettings => 'UI-Sprache';

  @override
  String get aboutApp => 'Über';

  @override
  String get version => 'Version';

  @override
  String get appVersion => '1.0.0';

  @override
  String get copyright => '© 2025';

  @override
  String filesAdded(int count) {
    return '$count Dateien hinzugefügt';
  }

  @override
  String get cannotSelectFiles =>
      'Dateien können nicht ausgewählt werden, bitte Berechtigungen überprüfen';

  @override
  String get selectLanguage => 'Sprache auswählen';

  @override
  String get systemLanguage => 'Systemsprache';

  @override
  String get english => 'English';

  @override
  String get spanish => 'Español';

  @override
  String get portuguese => 'Português';

  @override
  String get french => 'Français';

  @override
  String get german => 'Deutsch';

  @override
  String get chineseSimplified => '简体中文';

  @override
  String get chineseTraditional => '繁體中文';

  @override
  String get japanese => '日本語';

  @override
  String get korean => '한국어';

  @override
  String currentLanguage(String language) {
    return 'Aktuell: $language';
  }

  @override
  String get selectAuthorizedDirectory => 'Autorisiertes Verzeichnis verwalten';

  @override
  String get selectAuthorizedDirectorySubtitle =>
      'Zugriff auf iPhone/iPad-Dateiverzeichnisse autorisieren';

  @override
  String get shareApp => 'Teilen';

  @override
  String get shareAppSubtitle => 'Diese App mit Freunden teilen';

  @override
  String get rateApp => 'Bewerten';

  @override
  String get rateAppSubtitle => 'Bewerten Sie uns im App Store';

  @override
  String get aboutAppSubtitle => 'App-Version, Richtlinien und mehr';

  @override
  String get privacyPolicy => 'Datenschutzrichtlinie';

  @override
  String get termsOfService => 'Nutzungsbedingungen';

  @override
  String get appDescription =>
      'Ein schöner Dokumenten-Hub für PDF-, Word-, Excel- und PowerPoint-Dateien mit erweiterter Suche, Sortierung und PDF-Erstellungsfähigkeiten.';

  @override
  String get authorizedDirectories => 'Autorisierte Verzeichnisse verwalten';

  @override
  String get addDirectory => 'Verzeichnis hinzufügen';

  @override
  String get removeDirectory => 'Entfernen';

  @override
  String get noAuthorizedDirectories => 'Keine autorisierten Verzeichnisse';

  @override
  String get noAuthorizedDirectoriesSubtitle =>
      'Verzeichnisse hinzufügen, um auf Dateien von diesen Standorten zuzugreifen';

  @override
  String get directoryAdded => 'Verzeichnis erfolgreich autorisiert';

  @override
  String get directoryRemoved => 'Verzeichnis-Autorisierung entfernt';

  @override
  String get cannotAddDirectory =>
      'Verzeichnis kann nicht hinzugefügt werden, bitte versuchen Sie es erneut';

  @override
  String appCanAccessDirectories(String appName) {
    return '$appName kann auf die folgenden Verzeichnisse zugreifen:';
  }

  @override
  String filesCount(int count) {
    return '$count Dateien';
  }

  @override
  String directorySize(String size) {
    return 'Größe: $size';
  }

  @override
  String get cancelAuthorization => 'Autorisierung abbrechen';

  @override
  String confirmCancelAuthorization(String appName, String directoryName) {
    return 'Möchten Sie die Zugriffsberechtigung für $appName’s $directoryName wirklich aufheben? Nach der Aufhebung sind die Dateien in diesem Verzeichnis nicht mehr sichtbar.';
  }

  @override
  String get noKeepIt => 'Nein, behalten';

  @override
  String get yesRemoveFolder => 'Ja, ich brauche diesen Ordner nicht mehr';

  @override
  String get editName => 'Namen bearbeiten';

  @override
  String get enterCustomName => 'Benutzerdefinierten Namen eingeben';

  @override
  String get customName => 'Benutzerdefinierter Name';

  @override
  String get save => 'Speichern';

  @override
  String get cancel => 'Abbrechen';

  @override
  String get nameForNewFolder =>
      'Geben Sie zur späteren einfachen Identifizierung einen Namen für den gerade ausgewählten App-Ordner ein';

  @override
  String defaultAppName(int number) {
    return 'App $number';
  }

  @override
  String get selectFiles => 'Dateien auswählen';

  @override
  String get authorizeFolder => 'Ordner autorisieren';

  @override
  String get selectingDirectory => 'Verzeichnis wird gescannt...';

  @override
  String directorySelected(String name) {
    return 'Ausgewähltes Verzeichnis: $name';
  }

  @override
  String get directorySelectionOnlyMobile =>
      'Verzeichnisauswahl ist nur auf iOS/Android verfügbar';

  @override
  String get cannotSelectDirectory =>
      'Verzeichnis kann nicht ausgewählt werden, bitte Berechtigungen überprüfen';

  @override
  String loadedFiles(int count) {
    return '$count Dateien gescannt';
  }

  @override
  String get cannotLoadDirectoryFiles =>
      'Verzeichnisdateien können nicht geladen werden';

  @override
  String get iosPermissionMessage =>
      'Aufgrund der iPhone-Berechtigungsbeschränkungen müssen Sie zuerst den Zugriff auf die Ordner autorisieren, die Ihre PDF-, Word-, Excel- und PPT-Dateien enthalten.';

  @override
  String get noFilesFoundMessage =>
      'Keine Dateien dieses Typs gefunden.\nBitte wählen Sie andere Typen von oben aus oder tippen Sie auf die \'+\'-Schaltfläche, um weitere Dateien hinzuzufügen.';

  @override
  String get noFilesFoundGeneral =>
      'Keine Dateien gefunden, nach unten ziehen zum erneuten Scannen.';

  @override
  String get importFilesFromPhone => 'Dateien vom Telefon importieren';

  @override
  String get selectFolder => 'Autorisierten Ordner hinzufügen';

  @override
  String get selectFolderSubtitle =>
      'Ordnerzugriff gewähren um Dateien zu scannen und anzuzeigen';

  @override
  String get photoToPdf => 'Foto zu PDF';

  @override
  String get photoToPdfSubtitle =>
      'Verwenden Sie die Handykamera, um mehrere Fotos aufzunehmen und automatisch zu PDF zusammenzuführen';

  @override
  String get mergeImagesToPdf => 'Bilder zu PDF zusammenführen';

  @override
  String get mergeImagesToPdfSubtitle => 'Album-Bilder zu PDF zusammenführen';

  @override
  String get convert => 'Konvertieren';

  @override
  String get pdfSavedSuccessfully => 'PDF erfolgreich gespeichert';

  @override
  String get pdfSaveFailed => 'PDF-Speicherung fehlgeschlagen';

  @override
  String get undo => 'Rückgängig';

  @override
  String get deleteImage => 'Bild löschen';

  @override
  String get confirmDeleteImage =>
      'Sind Sie sicher, dass Sie dieses Bild löschen möchten?';

  @override
  String get delete => 'Löschen';

  @override
  String get reorderImage => 'Bilder neu ordnen';

  @override
  String get takePicture => 'Foto aufnehmen';

  @override
  String addImagesFromGallery(int count) {
    return '$count Bilder aus der Galerie hinzufügen';
  }

  @override
  String get cropImage => 'Bild zuschneiden';

  @override
  String confirmUndo(String action) {
    return 'Rückgängig: $action. Dies macht Ihre letzte Aktion rückgängig. Fortfahren?';
  }

  @override
  String get cropPlaceholderMessage =>
      'Zuschneidefunktion wird hier implementiert';

  @override
  String get cropFeatureComingSoon =>
      'Erweiterte Zuschneidefunktionen kommen bald!';

  @override
  String get addMoreImages => 'Mehr Bilder hinzufügen';

  @override
  String get takePhoto => 'Foto aufnehmen';

  @override
  String get importFromAlbum => 'Aus Album importieren';

  @override
  String get sort => 'Sortieren';

  @override
  String get nameSort => 'Name';

  @override
  String get lastModified => 'Zuletzt geändert';

  @override
  String get sizeSort => 'Größe';

  @override
  String get descending => 'Absteigend';

  @override
  String get apply => 'Anwenden';

  @override
  String get noRecentFiles => 'Keine kürzlich verwendeten Dateien';

  @override
  String get noFavoriteFiles => 'Sie haben noch keine Dateien favorisiert';

  @override
  String get noFavoriteFilesHint =>
      'Sie können Dateien favorisieren, indem Sie das ☆ Stern-Symbol neben den Dateien auf der Dokumentenseite antippen';

  @override
  String get fileNotFound => 'Datei nicht gefunden';

  @override
  String get weLikeYouToo => 'Wir mögen Sie auch!';

  @override
  String get thankYouForFeedback => 'Danke für Ihr Feedback.';

  @override
  String get theBestWeCanGet => 'Das Beste, was wir bekommen können :)';

  @override
  String get maybeLater => 'Vielleicht später';

  @override
  String get rateNow => 'Bewerten';

  @override
  String get discardChangesTitle => 'Discard changes?';

  @override
  String get discardChangesContent =>
      'You have unsaved changes. If you exit now, they will be lost.';

  @override
  String get discard => 'Discard';

  @override
  String get keepEditing => 'Keep Editing';
}
