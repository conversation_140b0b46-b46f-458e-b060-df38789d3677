// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Korean (`ko`).
class AppLocalizationsKo extends AppLocalizations {
  AppLocalizationsKo([String locale = 'ko']) : super(locale);

  @override
  String get appName => '올 문서 허브';

  @override
  String get documents => '문서';

  @override
  String get recent => '최근';

  @override
  String get favorite => '즐겨찾기';

  @override
  String get settings => '설정';

  @override
  String get addFiles => '파일 추가';

  @override
  String get addFilesSubtitle => '관리할 기기에서 더 많은 파일을 선택하세요';

  @override
  String get followDocumentTheme => '문서 유형 색상 구성표 따르기';

  @override
  String get followDocumentThemeSubtitle => 'PDF/Word/Excel/PPT 스타일';

  @override
  String get languageSettings => 'UI 언어';

  @override
  String get aboutApp => '정보';

  @override
  String get version => '버전';

  @override
  String get appVersion => '1.0.0';

  @override
  String get copyright => '© 2025';

  @override
  String filesAdded(int count) {
    return '$count개 파일이 추가되었습니다';
  }

  @override
  String get cannotSelectFiles => '파일을 선택할 수 없습니다. 권한을 확인하세요';

  @override
  String get selectLanguage => '언어 선택';

  @override
  String get systemLanguage => '시스템 언어';

  @override
  String get english => 'English';

  @override
  String get spanish => 'Español';

  @override
  String get portuguese => 'Português';

  @override
  String get french => 'Français';

  @override
  String get german => 'Deutsch';

  @override
  String get chineseSimplified => '简体中文';

  @override
  String get chineseTraditional => '繁體中文';

  @override
  String get japanese => '日本語';

  @override
  String get korean => '한국어';

  @override
  String currentLanguage(String language) {
    return '현재: $language';
  }

  @override
  String get selectAuthorizedDirectory => '인증된 디렉토리 관리';

  @override
  String get selectAuthorizedDirectorySubtitle => 'iPhone/iPad 파일 디렉토리 액세스 인증';

  @override
  String get shareApp => '공유';

  @override
  String get shareAppSubtitle => '친구들과 이 앱을 공유하세요';

  @override
  String get rateApp => '평가';

  @override
  String get rateAppSubtitle => 'App Store에서 평가해주세요';

  @override
  String get aboutAppSubtitle => '본 앱의 버전, 정책 등';

  @override
  String get privacyPolicy => '개인정보 처리방침';

  @override
  String get termsOfService => '서비스 약관';

  @override
  String get appDescription =>
      'PDF, Word, Excel, PowerPoint 파일에 대한 고급 검색, 정렬 및 PDF 생성 기능을 갖춘 아름다운 문서 허브입니다.';

  @override
  String get authorizedDirectories => '인증된 디렉토리 관리';

  @override
  String get addDirectory => '디렉토리 추가';

  @override
  String get removeDirectory => '제거';

  @override
  String get noAuthorizedDirectories => '인증된 디렉토리 없음';

  @override
  String get noAuthorizedDirectoriesSubtitle => '해당 위치의 파일에 액세스하려면 디렉토리를 추가하세요';

  @override
  String get directoryAdded => '디렉토리 인증 성공';

  @override
  String get directoryRemoved => '디렉토리 인증이 제거되었습니다';

  @override
  String get cannotAddDirectory => '디렉토리를 추가할 수 없습니다. 다시 시도하세요';

  @override
  String appCanAccessDirectories(String appName) {
    return '$appName은(는) 다음 디렉토리에 액세스할 수 있습니다:';
  }

  @override
  String filesCount(int count) {
    return '$count개 파일';
  }

  @override
  String directorySize(String size) {
    return '크기: $size';
  }

  @override
  String get cancelAuthorization => '권한 취소';

  @override
  String confirmCancelAuthorization(String appName, String directoryName) {
    return '$appName의 $directoryName에 대한 액세스 권한을 정말로 취소하시겠습니까? 취소 후 이 디렉토리의 파일은 더 이상 표시되지 않습니다.';
  }

  @override
  String get noKeepIt => '아니요, 유지합니다';

  @override
  String get yesRemoveFolder => '예, 이 폴더는 더 이상 필요하지 않습니다';

  @override
  String get editName => '이름 편집';

  @override
  String get enterCustomName => '사용자 지정 이름 입력';

  @override
  String get customName => '사용자 지정 이름';

  @override
  String get save => '저장';

  @override
  String get cancel => '취소';

  @override
  String get nameForNewFolder => '나중에 쉽게 식별할 수 있도록 방금 선택한 앱 폴더에 이름을 지정하십시오';

  @override
  String defaultAppName(int number) {
    return '앱 $number';
  }

  @override
  String get selectFiles => '파일 선택';

  @override
  String get authorizeFolder => '폴더 권한 부여';

  @override
  String get selectingDirectory => '디렉토리 스캔 중...';

  @override
  String directorySelected(String name) {
    return '선택된 디렉토리: $name';
  }

  @override
  String get directorySelectionOnlyMobile => '디렉토리 선택은 iOS/Android에서만 사용 가능합니다';

  @override
  String get cannotSelectDirectory => '디렉토리를 선택할 수 없습니다. 권한을 확인하세요';

  @override
  String loadedFiles(int count) {
    return '$count개 파일을 스캔했습니다';
  }

  @override
  String get cannotLoadDirectoryFiles => '디렉토리 파일을 로드할 수 없습니다';

  @override
  String get iosPermissionMessage =>
      'iPhone 권한 제한으로 인해, PDF, Word, Excel, PPT 파일이 포함된 폴더에 대한 액세스를 먼저 권한을 부여해야 합니다.';

  @override
  String get noFilesFoundMessage =>
      '이 유형의 파일을 찾을 수 없습니다.\n위에서 다른 유형을 선택하거나, \'+\'버튼을 눌러 더 많은 파일을 추가하세요.';

  @override
  String get noFilesFoundGeneral => '파일을 찾을 수 없습니다. 아래로 당겨서 다시 스캔하세요.';

  @override
  String get importFilesFromPhone => '휴대폰에서 파일 가져오기';

  @override
  String get selectFolder => '권한 폴더 추가';

  @override
  String get selectFolderSubtitle => '폴더 액세스를 허용하여 파일을 스캔하고 표시';

  @override
  String get photoToPdf => '사진을 PDF로';

  @override
  String get photoToPdfSubtitle => '휴대폰 카메라로 여러 장의 사진을 촬영하여 자동으로 PDF로 병합';

  @override
  String get mergeImagesToPdf => '이미지를 PDF로 병합';

  @override
  String get mergeImagesToPdfSubtitle => '앨범 이미지를 PDF로 합병';

  @override
  String get convert => '변환';

  @override
  String get pdfSavedSuccessfully => 'PDF 저장 성공';

  @override
  String get pdfSaveFailed => 'PDF 저장 실패';

  @override
  String get undo => '실행 취소';

  @override
  String get deleteImage => '이미지 삭제';

  @override
  String get confirmDeleteImage => '이 이미지를 삭제하시겠습니까?';

  @override
  String get delete => '삭제';

  @override
  String get reorderImage => '이미지 순서 변경';

  @override
  String get takePicture => '사진 촬영';

  @override
  String addImagesFromGallery(int count) {
    return '갤러리에서 $count개 이미지 추가';
  }

  @override
  String get cropImage => '이미지 자르기';

  @override
  String confirmUndo(String action) {
    return '실행 취소: $action. 마지막 작업을 되돌립니다. 계속하시겠습니까?';
  }

  @override
  String get cropPlaceholderMessage => '자르기 기능이 여기에 구현됩니다';

  @override
  String get cropFeatureComingSoon => '고급 자르기 기능이 곷 출시됩니다!';

  @override
  String get addMoreImages => '더 많은 이미지 추가';

  @override
  String get takePhoto => '사진 촬영';

  @override
  String get importFromAlbum => '앨범에서 가져오기';

  @override
  String get sort => '정렬';

  @override
  String get nameSort => '이름';

  @override
  String get lastModified => '마지막 수정일';

  @override
  String get sizeSort => '크기';

  @override
  String get descending => '내림차순';

  @override
  String get apply => '적용';

  @override
  String get noRecentFiles => '최근 파일이 없습니다';

  @override
  String get noFavoriteFiles => '아직 즐겨찾기에 추가한 파일이 없습니다';

  @override
  String get noFavoriteFilesHint =>
      '문서 페이지에서 파일 옆의 ☆별 아이콘을 탭하여 즐겨찾기에 추가할 수 있습니다';

  @override
  String get fileNotFound => '파일을 찾을 수 없습니다';

  @override
  String get weLikeYouToo => '저희도 당신을 좋아합니다!';

  @override
  String get thankYouForFeedback => '피드백을 주셔서 감사합니다.';

  @override
  String get theBestWeCanGet => '최고의 평가입니다 :)';

  @override
  String get maybeLater => '나중에';

  @override
  String get rateNow => '평가하기';

  @override
  String get discardChangesTitle => 'Discard changes?';

  @override
  String get discardChangesContent =>
      'You have unsaved changes. If you exit now, they will be lost.';

  @override
  String get discard => 'Discard';

  @override
  String get keepEditing => 'Keep Editing';
}
