import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_de.dart';
import 'app_localizations_en.dart';
import 'app_localizations_es.dart';
import 'app_localizations_fr.dart';
import 'app_localizations_ja.dart';
import 'app_localizations_ko.dart';
import 'app_localizations_pt.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('de'),
    Locale('en'),
    Locale('es'),
    Locale('fr'),
    Locale('ja'),
    Locale('ko'),
    Locale('pt'),
    Locale('zh'),
    Locale('zh', 'TW'),
  ];

  /// No description provided for @appName.
  ///
  /// In en, this message translates to:
  /// **'All Document Hub'**
  String get appName;

  /// No description provided for @documents.
  ///
  /// In en, this message translates to:
  /// **'Documents'**
  String get documents;

  /// No description provided for @recent.
  ///
  /// In en, this message translates to:
  /// **'Recent'**
  String get recent;

  /// No description provided for @favorite.
  ///
  /// In en, this message translates to:
  /// **'Favorite'**
  String get favorite;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @addFiles.
  ///
  /// In en, this message translates to:
  /// **'Add Files'**
  String get addFiles;

  /// No description provided for @addFilesSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Choose more files from your device to manage'**
  String get addFilesSubtitle;

  /// No description provided for @followDocumentTheme.
  ///
  /// In en, this message translates to:
  /// **'Follow document type color scheme'**
  String get followDocumentTheme;

  /// No description provided for @followDocumentThemeSubtitle.
  ///
  /// In en, this message translates to:
  /// **'PDF/Word/Excel/PPT styles'**
  String get followDocumentThemeSubtitle;

  /// No description provided for @languageSettings.
  ///
  /// In en, this message translates to:
  /// **'UI Language'**
  String get languageSettings;

  /// No description provided for @aboutApp.
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get aboutApp;

  /// No description provided for @version.
  ///
  /// In en, this message translates to:
  /// **'Version'**
  String get version;

  /// No description provided for @appVersion.
  ///
  /// In en, this message translates to:
  /// **'1.0.0'**
  String get appVersion;

  /// No description provided for @copyright.
  ///
  /// In en, this message translates to:
  /// **'© 2025'**
  String get copyright;

  /// No description provided for @filesAdded.
  ///
  /// In en, this message translates to:
  /// **'Added {count} files'**
  String filesAdded(int count);

  /// No description provided for @cannotSelectFiles.
  ///
  /// In en, this message translates to:
  /// **'Cannot select files, please check permissions'**
  String get cannotSelectFiles;

  /// No description provided for @selectLanguage.
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get selectLanguage;

  /// No description provided for @systemLanguage.
  ///
  /// In en, this message translates to:
  /// **'System Language'**
  String get systemLanguage;

  /// No description provided for @english.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get english;

  /// No description provided for @spanish.
  ///
  /// In en, this message translates to:
  /// **'Español'**
  String get spanish;

  /// No description provided for @portuguese.
  ///
  /// In en, this message translates to:
  /// **'Português'**
  String get portuguese;

  /// No description provided for @french.
  ///
  /// In en, this message translates to:
  /// **'Français'**
  String get french;

  /// No description provided for @german.
  ///
  /// In en, this message translates to:
  /// **'Deutsch'**
  String get german;

  /// No description provided for @chineseSimplified.
  ///
  /// In en, this message translates to:
  /// **'简体中文'**
  String get chineseSimplified;

  /// No description provided for @chineseTraditional.
  ///
  /// In en, this message translates to:
  /// **'繁體中文'**
  String get chineseTraditional;

  /// No description provided for @japanese.
  ///
  /// In en, this message translates to:
  /// **'日本語'**
  String get japanese;

  /// No description provided for @korean.
  ///
  /// In en, this message translates to:
  /// **'한국어'**
  String get korean;

  /// No description provided for @currentLanguage.
  ///
  /// In en, this message translates to:
  /// **'Current: {language}'**
  String currentLanguage(String language);

  /// No description provided for @selectAuthorizedDirectory.
  ///
  /// In en, this message translates to:
  /// **'Manage Authorized Folders'**
  String get selectAuthorizedDirectory;

  /// No description provided for @selectAuthorizedDirectorySubtitle.
  ///
  /// In en, this message translates to:
  /// **'Authorize access to iPhone/iPad file directories'**
  String get selectAuthorizedDirectorySubtitle;

  /// No description provided for @shareApp.
  ///
  /// In en, this message translates to:
  /// **'Share'**
  String get shareApp;

  /// No description provided for @shareAppSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Share this app with friends'**
  String get shareAppSubtitle;

  /// No description provided for @rateApp.
  ///
  /// In en, this message translates to:
  /// **'Rate'**
  String get rateApp;

  /// No description provided for @rateAppSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Rate us on the App Store'**
  String get rateAppSubtitle;

  /// No description provided for @aboutAppSubtitle.
  ///
  /// In en, this message translates to:
  /// **'App version, policies and more'**
  String get aboutAppSubtitle;

  /// No description provided for @privacyPolicy.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicy;

  /// No description provided for @termsOfService.
  ///
  /// In en, this message translates to:
  /// **'Terms of Service'**
  String get termsOfService;

  /// No description provided for @appDescription.
  ///
  /// In en, this message translates to:
  /// **'A beautiful document hub for PDFs, Word, Excel, and PowerPoint files with advanced search, sorting, and PDF creation capabilities.'**
  String get appDescription;

  /// No description provided for @authorizedDirectories.
  ///
  /// In en, this message translates to:
  /// **'Manage Authorized Folders'**
  String get authorizedDirectories;

  /// No description provided for @addDirectory.
  ///
  /// In en, this message translates to:
  /// **'Add Directory'**
  String get addDirectory;

  /// No description provided for @removeDirectory.
  ///
  /// In en, this message translates to:
  /// **'Remove'**
  String get removeDirectory;

  /// No description provided for @noAuthorizedDirectories.
  ///
  /// In en, this message translates to:
  /// **'No authorized directories'**
  String get noAuthorizedDirectories;

  /// No description provided for @noAuthorizedDirectoriesSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Add directories to access files from those locations'**
  String get noAuthorizedDirectoriesSubtitle;

  /// No description provided for @directoryAdded.
  ///
  /// In en, this message translates to:
  /// **'Directory authorized successfully'**
  String get directoryAdded;

  /// No description provided for @directoryRemoved.
  ///
  /// In en, this message translates to:
  /// **'Directory authorization removed'**
  String get directoryRemoved;

  /// No description provided for @cannotAddDirectory.
  ///
  /// In en, this message translates to:
  /// **'Cannot add directory, please try again'**
  String get cannotAddDirectory;

  /// No description provided for @appCanAccessDirectories.
  ///
  /// In en, this message translates to:
  /// **'{appName} can access the following directories:'**
  String appCanAccessDirectories(String appName);

  /// No description provided for @filesCount.
  ///
  /// In en, this message translates to:
  /// **'{count} files'**
  String filesCount(int count);

  /// No description provided for @directorySize.
  ///
  /// In en, this message translates to:
  /// **'Size: {size}'**
  String directorySize(String size);

  /// No description provided for @cancelAuthorization.
  ///
  /// In en, this message translates to:
  /// **'Cancel Authorization'**
  String get cancelAuthorization;

  /// No description provided for @confirmCancelAuthorization.
  ///
  /// In en, this message translates to:
  /// **'Do you really want to cancel the access authorization for {appName}\'s {directoryName}? After cancellation, files in this directory will no longer be visible.'**
  String confirmCancelAuthorization(String appName, String directoryName);

  /// No description provided for @noKeepIt.
  ///
  /// In en, this message translates to:
  /// **'No, keep it'**
  String get noKeepIt;

  /// No description provided for @yesRemoveFolder.
  ///
  /// In en, this message translates to:
  /// **'Yes, I don\'t need this folder anymore'**
  String get yesRemoveFolder;

  /// No description provided for @editName.
  ///
  /// In en, this message translates to:
  /// **'Edit Name'**
  String get editName;

  /// No description provided for @enterCustomName.
  ///
  /// In en, this message translates to:
  /// **'Enter custom name'**
  String get enterCustomName;

  /// No description provided for @customName.
  ///
  /// In en, this message translates to:
  /// **'Custom Name'**
  String get customName;

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @nameForNewFolder.
  ///
  /// In en, this message translates to:
  /// **'For easy identification later, please give a name to the app folder you just selected'**
  String get nameForNewFolder;

  /// No description provided for @defaultAppName.
  ///
  /// In en, this message translates to:
  /// **'App {number}'**
  String defaultAppName(int number);

  /// No description provided for @selectFiles.
  ///
  /// In en, this message translates to:
  /// **'Select Files'**
  String get selectFiles;

  /// No description provided for @authorizeFolder.
  ///
  /// In en, this message translates to:
  /// **'Authorize Folder'**
  String get authorizeFolder;

  /// No description provided for @selectingDirectory.
  ///
  /// In en, this message translates to:
  /// **'Scanning directory...'**
  String get selectingDirectory;

  /// No description provided for @directorySelected.
  ///
  /// In en, this message translates to:
  /// **'Selected directory: {name}'**
  String directorySelected(String name);

  /// No description provided for @directorySelectionOnlyMobile.
  ///
  /// In en, this message translates to:
  /// **'Directory selection is only available on iOS/Android'**
  String get directorySelectionOnlyMobile;

  /// No description provided for @cannotSelectDirectory.
  ///
  /// In en, this message translates to:
  /// **'Cannot select directory, please check permissions'**
  String get cannotSelectDirectory;

  /// No description provided for @loadedFiles.
  ///
  /// In en, this message translates to:
  /// **'Scanned {count} files'**
  String loadedFiles(int count);

  /// No description provided for @cannotLoadDirectoryFiles.
  ///
  /// In en, this message translates to:
  /// **'Cannot load directory files'**
  String get cannotLoadDirectoryFiles;

  /// No description provided for @iosPermissionMessage.
  ///
  /// In en, this message translates to:
  /// **'Due to iPhone permission restrictions, you need to authorize access to the folders containing your PDF, Word, Excel, and PPT files first.'**
  String get iosPermissionMessage;

  /// No description provided for @noFilesFoundMessage.
  ///
  /// In en, this message translates to:
  /// **'No files of this type found.\nPlease select other types from above, or tap the \'+\' button to add more files.'**
  String get noFilesFoundMessage;

  /// No description provided for @noFilesFoundGeneral.
  ///
  /// In en, this message translates to:
  /// **'No files found, pull down to scan again.'**
  String get noFilesFoundGeneral;

  /// No description provided for @importFilesFromPhone.
  ///
  /// In en, this message translates to:
  /// **'Import files from phone'**
  String get importFilesFromPhone;

  /// No description provided for @selectFolder.
  ///
  /// In en, this message translates to:
  /// **'Add Authorized Folder'**
  String get selectFolder;

  /// No description provided for @selectFolderSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Grant folder access to scan and display files'**
  String get selectFolderSubtitle;

  /// No description provided for @photoToPdf.
  ///
  /// In en, this message translates to:
  /// **'Photo to PDF'**
  String get photoToPdf;

  /// No description provided for @photoToPdfSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Use camera to take multiple photos and merge into PDF'**
  String get photoToPdfSubtitle;

  /// No description provided for @mergeImagesToPdf.
  ///
  /// In en, this message translates to:
  /// **'Merge Images to PDF'**
  String get mergeImagesToPdf;

  /// No description provided for @mergeImagesToPdfSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Merge album photos to PDF'**
  String get mergeImagesToPdfSubtitle;

  /// No description provided for @convert.
  ///
  /// In en, this message translates to:
  /// **'Convert'**
  String get convert;

  /// No description provided for @pdfSavedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'PDF saved successfully'**
  String get pdfSavedSuccessfully;

  /// No description provided for @pdfSaveFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to save PDF'**
  String get pdfSaveFailed;

  /// No description provided for @undo.
  ///
  /// In en, this message translates to:
  /// **'Undo'**
  String get undo;

  /// No description provided for @deleteImage.
  ///
  /// In en, this message translates to:
  /// **'Delete Image'**
  String get deleteImage;

  /// No description provided for @confirmDeleteImage.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this image?'**
  String get confirmDeleteImage;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @reorderImage.
  ///
  /// In en, this message translates to:
  /// **'Reorder images'**
  String get reorderImage;

  /// No description provided for @takePicture.
  ///
  /// In en, this message translates to:
  /// **'Take picture'**
  String get takePicture;

  /// No description provided for @addImagesFromGallery.
  ///
  /// In en, this message translates to:
  /// **'Add {count} images from gallery'**
  String addImagesFromGallery(int count);

  /// No description provided for @cropImage.
  ///
  /// In en, this message translates to:
  /// **'Crop Image'**
  String get cropImage;

  /// No description provided for @confirmUndo.
  ///
  /// In en, this message translates to:
  /// **'Undo: {action}. This will revert your last action. Continue?'**
  String confirmUndo(String action);

  /// No description provided for @cropPlaceholderMessage.
  ///
  /// In en, this message translates to:
  /// **'Crop functionality will be implemented here'**
  String get cropPlaceholderMessage;

  /// No description provided for @cropFeatureComingSoon.
  ///
  /// In en, this message translates to:
  /// **'Advanced crop features coming soon!'**
  String get cropFeatureComingSoon;

  /// No description provided for @addMoreImages.
  ///
  /// In en, this message translates to:
  /// **'Add more images'**
  String get addMoreImages;

  /// No description provided for @takePhoto.
  ///
  /// In en, this message translates to:
  /// **'Take Photo'**
  String get takePhoto;

  /// No description provided for @importFromAlbum.
  ///
  /// In en, this message translates to:
  /// **'Import from Album'**
  String get importFromAlbum;

  /// No description provided for @sort.
  ///
  /// In en, this message translates to:
  /// **'Sort'**
  String get sort;

  /// No description provided for @nameSort.
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get nameSort;

  /// No description provided for @lastModified.
  ///
  /// In en, this message translates to:
  /// **'Last Modified'**
  String get lastModified;

  /// No description provided for @sizeSort.
  ///
  /// In en, this message translates to:
  /// **'Size'**
  String get sizeSort;

  /// No description provided for @descending.
  ///
  /// In en, this message translates to:
  /// **'Descending'**
  String get descending;

  /// No description provided for @apply.
  ///
  /// In en, this message translates to:
  /// **'Apply'**
  String get apply;

  /// No description provided for @noRecentFiles.
  ///
  /// In en, this message translates to:
  /// **'No recent files'**
  String get noRecentFiles;

  /// No description provided for @noFavoriteFiles.
  ///
  /// In en, this message translates to:
  /// **'You haven\'t favorited any files yet'**
  String get noFavoriteFiles;

  /// No description provided for @noFavoriteFilesHint.
  ///
  /// In en, this message translates to:
  /// **'You can favorite files by tapping the ☆ star icon next to files on the documents page'**
  String get noFavoriteFilesHint;

  /// No description provided for @fileNotFound.
  ///
  /// In en, this message translates to:
  /// **'File not found'**
  String get fileNotFound;

  /// No description provided for @weLikeYouToo.
  ///
  /// In en, this message translates to:
  /// **'We like you too!'**
  String get weLikeYouToo;

  /// No description provided for @thankYouForFeedback.
  ///
  /// In en, this message translates to:
  /// **'Thank for your feedback.'**
  String get thankYouForFeedback;

  /// No description provided for @theBestWeCanGet.
  ///
  /// In en, this message translates to:
  /// **'The best we can get :)'**
  String get theBestWeCanGet;

  /// No description provided for @maybeLater.
  ///
  /// In en, this message translates to:
  /// **'Maybe later'**
  String get maybeLater;

  /// No description provided for @rateNow.
  ///
  /// In en, this message translates to:
  /// **'Rate'**
  String get rateNow;

  /// No description provided for @discardChangesTitle.
  ///
  /// In en, this message translates to:
  /// **'Discard changes?'**
  String get discardChangesTitle;

  /// No description provided for @discardChangesContent.
  ///
  /// In en, this message translates to:
  /// **'You have unsaved changes. If you exit now, they will be lost.'**
  String get discardChangesContent;

  /// No description provided for @discard.
  ///
  /// In en, this message translates to:
  /// **'Discard'**
  String get discard;

  /// No description provided for @keepEditing.
  ///
  /// In en, this message translates to:
  /// **'Keep Editing'**
  String get keepEditing;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>[
    'de',
    'en',
    'es',
    'fr',
    'ja',
    'ko',
    'pt',
    'zh',
  ].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when language+country codes are specified.
  switch (locale.languageCode) {
    case 'zh':
      {
        switch (locale.countryCode) {
          case 'TW':
            return AppLocalizationsZhTw();
        }
        break;
      }
  }

  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'de':
      return AppLocalizationsDe();
    case 'en':
      return AppLocalizationsEn();
    case 'es':
      return AppLocalizationsEs();
    case 'fr':
      return AppLocalizationsFr();
    case 'ja':
      return AppLocalizationsJa();
    case 'ko':
      return AppLocalizationsKo();
    case 'pt':
      return AppLocalizationsPt();
    case 'zh':
      return AppLocalizationsZh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
