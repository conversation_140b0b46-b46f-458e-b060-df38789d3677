# All PDF Editor

本产品是一款Android/iOS上的跨平台PDF编辑工具。它可以：
1. 自动扫描手机上有哪些 PDF、Word、Excel、PPT 文件，并显示在列表中；
2. 可以打开和编辑 Word、Excel、PPT 里的文字；可以把 Word、Excel、PPT 保存为 PDF 文件；
3. 可以给PDF加上用户手写的签字，可以在PDF上用各种颜色标记；
4. 可以将图片批量合并成PDF文件.

## 授权目录
由于Android/iOS沙盒机制的限制，要访问手机上的文件，需要用户授权。特别是在iOS上，如果没有授权，App实际上访问不了任何文件，列表只能显示空的。因此，在iOS上，首次运行时，列表位置会先显示一个引导信息：“由于iPhone的权限限制，您需要先授权访问您的PDF、Word、Excel、PPT文件所在的文件夹。” 并提供一个授权按钮。点击以后打开系统的文件夹授权界面。Android上则首次运行时先弹出系统授权获取访问外部存储的界面。在设置界面中，用户还可以管理授权目录：追加更多的授权目录，或删除不需要的授权目录。

- 管理授权目录：在设置 Tab 中的“管理授权文件夹”
- 授权管理目录的列表，显示授权的应用名称：
  - iOS：尽量根据容器与 iCloud 路径进行识别；如果无法识别，则进入编辑模式，用户可以手动输入一个友好的名称。列表里每项都有一个编辑图标，点击以后可以随时再编辑显示的名称。
  - Android：对于路径中包含 `/Android/data`/`/Android/media` 的目录，会解析出包名并通过系统的 PackageManager 显示对应的应用名称。
  - 常见路径（如 LocalSend、Dropbox、OneDrive 等）会被自动识别并格式化为友好的名称。
