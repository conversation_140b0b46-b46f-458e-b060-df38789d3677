## 编译器优化的调试日志规范

### Dart/Flutter 调试日志
在Flutter代码中使用调试日志时，必须用 `if (kDebugMode)` 包围，确保Release版本中完全优化掉：

```dart
import 'package:flutter/foundation.dart';

// ✅ 正确方式 - Release版本中完全优化
if (kDebugMode) {
  print('[ComponentName] Debug message: $value');
}

// ❌ 错误方式 - Release版本中仍有开销
print('[ComponentName] Debug message: $value');
```

**优势**：
- Release版本中代码块和字符串插值完全移除
- 零运行时开销
- 编译器保证优化

### Swift/iOS 调试日志
在Swift代码中使用调试日志时，必须用 `#if DEBUG` 条件编译包围：

```swift
// ✅ 正确方式 - Release版本中完全优化
#if DEBUG
print("[ComponentName] Debug message: \(value)")
#endif

// ❌ 错误方式1 - 函数调用可能保留
private func debugLog(_ message: String) {
  #if DEBUG
  print(message)
  #endif
}
debugLog("[ComponentName] Debug message: \(value)")

// ❌ 错误方式2 - Release版本中仍有开销
print("[ComponentName] Debug message: \(value)")
```

**优势**：
- Release版本中代码块、字符串插值、参数计算完全移除
- 100%保证零开销
- 不依赖编译器优化级别

### 注意事项
1. **字符串插值**：只有在条件编译内部的字符串插值才会被完全优化
2. **复杂计算**：如果日志需要复杂计算，也要放在条件内部
3. **一致性**：项目中所有调试日志都应遵循此规范
