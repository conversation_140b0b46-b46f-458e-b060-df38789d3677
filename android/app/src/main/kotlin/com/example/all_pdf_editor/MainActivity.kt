package com.example.all_pdf_editor

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.documentfile.provider.DocumentFile
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val channelName = "directory_picker"
    private var pendingResult: MethodChannel.Result? = null
    private lateinit var openTreeLauncher: ActivityResultLauncher<Uri?>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        openTreeLauncher = registerForActivityResult(ActivityResultContracts.OpenDocumentTree()) { uri ->
            val result = pendingResult
            pendingResult = null
            if (result == null) return@registerForActivityResult

            if (uri == null) {
                result.error("CANCELLED", "Directory selection was cancelled", null)
                return@registerForActivityResult
            }

            // Persist permissions for future access
            try {
                val flags = Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_GRANT_WRITE_URI_PERMISSION
                contentResolver.takePersistableUriPermission(uri, flags)
            } catch (_: SecurityException) {
            }

            val doc = DocumentFile.fromTreeUri(this, uri)
            val name = doc?.name ?: uri.lastPathSegment ?: "Folder"

            // Optionally store the URI in preferences for bookkeeping
            val prefs = getSharedPreferences("directory_picker", MODE_PRIVATE)
            val set = prefs.getStringSet("savedTreeUris", mutableSetOf())?.toMutableSet() ?: mutableSetOf()
            set.add(uri.toString())
            prefs.edit().putStringSet("savedTreeUris", set).apply()

            result.success(mapOf(
                "path" to uri.toString(),
                "name" to name,
                "success" to true
            ))
        }
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, channelName)
            .setMethodCallHandler { call: MethodCall, result: MethodChannel.Result ->
                when (call.method) {
                    "pickDirectory" -> pickDirectory(result)
                    "getDirectoryStats" -> getDirectoryStats(call, result)
                    "getDirectoryFilesForPath" -> getDirectoryFilesForPath(call, result)
                    else -> result.notImplemented()
                }
            }
    }

    private fun pickDirectory(result: MethodChannel.Result) {
        if (pendingResult != null) {
            result.error("BUSY", "A directory pick operation is already in progress", null)
            return
        }
        pendingResult = result
        openTreeLauncher.launch(null)
    }

    private fun getDirectoryStats(call: MethodCall, result: MethodChannel.Result) {
        val targetPath = call.argument<String>("path")
        if (targetPath.isNullOrEmpty()) {
            result.error("BAD_ARGS", "Missing 'path' argument", null)
            return
        }
        val uri = Uri.parse(targetPath)
        val root = DocumentFile.fromTreeUri(this, uri)
        if (root == null || !root.exists()) {
            result.error("NO_URI", "Invalid tree URI", targetPath)
            return
        }
        var fileCount = 0
        var totalBytes = 0L
        fun walk(dir: DocumentFile) {
            dir.listFiles().forEach { f ->
                if (f.isDirectory) {
                    walk(f)
                } else {
                    fileCount += 1
                    val len = try { f.length() } catch (_: Exception) { 0L }
                    totalBytes += len
                }
            }
        }
        try {
            walk(root)
            result.success(mapOf("fileCount" to fileCount, "totalBytes" to totalBytes))
        } catch (e: Exception) {
            result.error("ENUM_ERROR", "Failed to enumerate: ${e.message}", null)
        }
    }

    private fun getDirectoryFilesForPath(call: MethodCall, result: MethodChannel.Result) {
        val targetPath = call.argument<String>("path")
        if (targetPath.isNullOrEmpty()) {
            result.error("BAD_ARGS", "Missing 'path' argument", null)
            return
        }
        val uri = Uri.parse(targetPath)
        val root = DocumentFile.fromTreeUri(this, uri)
        if (root == null || !root.exists()) {
            result.error("NO_URI", "Invalid tree URI", targetPath)
            return
        }
        val files = mutableListOf<Map<String, Any>>()
        fun walk(dir: DocumentFile) {
            dir.listFiles().forEach { f ->
                if (f.isDirectory) {
                    walk(f)
                } else {
                    val size = try { f.length() } catch (_: Exception) { 0L }
                    val name = f.name ?: ""
                    val map = hashMapOf<String, Any>(
                        "name" to name,
                        "path" to (f.uri.toString()),
                        "size" to size,
                        "isDirectory" to false
                    )
                    files.add(map)
                }
            }
        }
        try {
            walk(root)
            result.success(files)
        } catch (e: Exception) {
            result.error("ENUM_ERROR", "Failed to enumerate: ${e.message}", null)
        }
    }
}
