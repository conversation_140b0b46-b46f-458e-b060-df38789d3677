## 问题

我的应用使用Security-Scoped Bookmarks访问用户授权的外部文件夹。当用户在应用设置中撤销所有文件夹访问权限后，理论上应该无法再访问这些文件。但实际情况是：

  1. 权限撤销操作：调用了revokeAuthorization方法，清除了UserDefaults中的bookmark数据
  2. iOS原生层确认：getDirectoryFiles仍返回92个文件，说明iOS底层的bookmark缓存未被清除
  3. 文件访问测试：撤销权限后，这些文件仍然可以正常打开和读取

  技术细节：
  - 使用标准的startAccessingSecurityScopedResource()和stopAccessingSecurityScopedResource()
  - 正确调用了iOS原生的bookmark撤销API
  - SharedPreferences已清空，但UserDefaults中的bookmark数据似乎持久化

## 原因

* **这不是已知的 iOS 沙盒设计缺陷**，更像是**会话内的权限仍处于“已开启”状态**或**持久化书签/文件句柄未完全释放**导致的继续可读。
  苹果的设计是：当你对某个通过安全域书签（security‑scoped bookmark）解析得到的 URL 调用 `startAccessingSecurityScopedResource()` 后，只要**没有对同一 URL 做“次数匹配”的 `stop…`**（并且关闭了相关文件句柄/枚举器），该**进程会继续拥有访问权**；只有最后一次 `stop…` 调用发生时，权限才会立刻收回。([Apple Developer][1])

* **没有“系统级一键撤销所有书签”的官方 API。** 苹果 DTS 工程师明确说明：**用户层面并没有受支持的方式去撤销某个 app 之前保存的安全域书签**；书签是应用自己保存与管理的（UserDefaults、Keychain、数据库等），撤销要靠应用自己“忘记”并停止访问。([forums.developer.apple.com][2])

* iOS 的“**设置 > 隐私与安全性 > 文件与文件夹**”可以**整体撤销某 app 的文件/文件夹访问能力**（这会阻断未来的访问/解析流程，开发者需正确处理失败路径），但它**不会在你的进程里强制关闭已经打开的会话与句柄**；通常需要应用重启后才完全体现效果。相关说明见 WWDC/开发者资料与实践文章。([Apple Developer][3], [Use Your Loaf][4])

---

## 为什么你会“撤销后仍能访问”

结合你的现象（撤销后还能枚举 92 个文件并成功读取），最常见原因有：

1. **start/stop 调用不对称（未完全平衡）**
   对同一 URL 多次 `startAccessing…` 必须**等量**调用 `stopAccessing…`，否则权限仍保持。苹果文档明确要求“成对/等量”调用；否则不仅权限不收回，还可能泄漏内核资源。([Apple Developer][1])

2. **仍有“活跃的文件句柄/目录枚举器/文件协调器”**
   就算你删除了持久化的 bookmark 数据，只要进程里还持有由该安全域派生出的**已打开句柄**（`FileHandle`/`NSFileCoordinator`/`DispatchIO`/`FileManager` 的目录枚举器），配合未平衡的 `startAccessing…`，读取就会继续成功（直到进程退出或最后一次 `stop…`）。

3. **“撤销”仅清理了你的一份持久化**
   安全域书签是**应用自管**的：你可以把它放在 UserDefaults、Keychain、数据库、App Group 等任何存储里。你清的是哪一份？是否有其它副本或历史迁移数据（例如老版本遗留、App Group 共享、Keychain）？苹果也强调书签完全由应用保存与再解析。([forums.developer.apple.com][2])

4. **对“revokeAuthorization”的误解**
   苹果平台里确有叫 `revokeAuthorization` 的 API，但它属于别的框架（例如 FamilyControls 的家长控制授权），**与文件书签无关**。安全域书签**没有**名为“revokeAuthorization”的官方撤销 API；正确做法是**停止访问 + 删除你保存的书签数据**。([Apple Developer][6])

## 最佳方案

1. **严格平衡**：为每个被解析的书签/URL，保证 `startAccessing…` 与 `stopAccessing…` **调用次数一致**。建议用一个集中管理器对 URL 做“引用计数”，并在 `defer` 中保证回收。([Apple Developer][1])
2. **关闭一切 I/O**：撤销前关闭所有 `FileHandle`/输入流/读写队列/目录枚举器；释放引用。
3. **删除持久化**：从 **UserDefaults / Keychain / 数据库 / App Group** 全量删除书签（包含所有历史 key）。
4. **进程级兜底**：在“撤销所有授权”这种敏感场景下，完成 1–3 步后**引导用户重启 app**，确保会话内残余全部消失。
5. **失败路径**：对所有文件操作加上“`startAccessing…` 失败/返回 false”与“权限错误（EACCES）”的处理 UI。

## 工程落地建议

集中管理 + RAII 保证回收

## 参考资料（关键语义出处）

* `startAccessingSecurityScopedResource`/`stopAccessingSecurityScopedResource` 的官方语义（需要成对调用；最后一次 `stop` 立刻失去访问）：苹果文档。([Apple Developer][1])
* **“用户层面没有受支持的方式撤销某条安全域书签”**：苹果开发者论坛，DTS 工程师答复。([forums.developer.apple.com][2])
* iOS 中“文件与文件夹”权限的用户可撤销能力（以及开发者需要处理撤销后的失败路径）：WWDC/实践文章。([Apple Developer][3], [Use Your Loaf][4])
* 你提到的 CVE 的官方描述与修复版本：NVD/Apple 安全文档聚合。([NVD][7])
* 其他背景参考（安全域书签持久化与状态问题综述，供延伸阅读）：([SwiftLee][8])


[1]: https://developer.apple.com/documentation/foundation/url/startaccessingsecurityscopedresource%28%29?utm_source=chatgpt.com "startAccessingSecurityScopedRe..."
[2]: https://forums.developer.apple.com/forums/thread/739198 "User removal of security scoped bo… | Apple Developer Forums"
[3]: https://developer.apple.com/videos/play/wwdc2019/719/?utm_source=chatgpt.com "What’s New in File Management and Quick Look - WWDC19 ..."
[4]: https://useyourloaf.com/blog/accessing-security-scoped-files/?utm_source=chatgpt.com "Accessing Security Scoped Files - Use Your Loaf"
[5]: https://nvd.nist.gov/vuln/detail/cve-2025-31191?utm_source=chatgpt.com "CVE-2025-31191 Detail - NVD"
[6]: https://developer.apple.com/documentation/familycontrols/authorizationcenter/revokeauthorization%28completionhandler%3A%29?utm_source=chatgpt.com "revokeAuthorization(completionHandler:)"
[7]: https://nvd.nist.gov/vuln/detail/CVE-2024-54468?utm_source=chatgpt.com "CVE-2024-54468 Detail - NVD"
[8]: https://www.avanderlee.com/swift/security-scoped-bookmarks-for-url-access/?utm_source=chatgpt.com "Security-scoped bookmarks for URL access"
