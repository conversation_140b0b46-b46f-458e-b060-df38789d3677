
# PDF 分辨率与页面尺寸处理说明

本文档旨在说明在图片合并生成PDF的过程中，系统是如何处理图片分辨率和PDF页面尺寸的。

核心原则是：**为每一批次的图片生成尺寸统一的PDF页面，以确保文档的规整和一致性。** 系统采用了一种基于中位数的动态计算方法来确定最合适的页面尺寸。

## 处理流程

当用户请求将一批处理后的图片合并成一个PDF文件时，系统会执行以下步骤：

### 1. 获取所有图片的原始尺寸

- 系统首先会解码每一张将要合并的图片（无论是经过AI裁剪还是手动处理后的图片）。
- 然后，它会分析并记录下每一张图片精确的宽度（width）和高度（height），单位为像素（pixels）。

### 2. 计算标准化的目标页面尺寸

- 为了避免因图片尺寸不一导致PDF页面大小不一，系统需要计算出一个“目标尺寸”（Target Dimension）。
- 计算方法如下：
  - 提取所有图片的高度、宽度和宽高比（`width / height`）。
  - 对所有宽度和宽高比分别进行排序，并找出**中位数（Median）**。
  - **目标宽度**被设定为宽度的中位数。
  - **目标高度**则通过“目标宽度 / 宽高比的中位数”计算得出。
- 这种方法可以有效地避免极端尺寸（过大或过小）的图片对最终页面尺寸产生过大影响，使得PDF页面尺寸更贴合大多数图片的普遍特征。

### 3. 统一并调整所有图片

- 在确定了目标页面尺寸后，系统会遍历所有图片，并使用 `sharp` 图像处理库将它们调整为统一尺寸。
- 调整策略分为两种：
  - **A. 拉伸/收缩**：如果单张图片的宽度与目标宽度相差不大（在0.5倍到2倍之间），系统会将其宽度直接调整为目标宽度，高度按比例缩放。这适用于大多数尺寸相似的图片。
  - **B. 适应并填充（Letterboxing）**：如果单张图片的尺寸与目标尺寸差异悬殊，为了避免过度拉伸导致图片内容严重变形，系统会创建一个目标大小的纯白背景画布，然后将原始图片按比例缩放后居中放置在这个画布上。这可以确保图片内容不变形，多余的部分由白边填充。

### 4. 创建并生成PDF文档

- 系统使用 `jsPDF` 库来创建PDF文档。
- PDF的页面物理尺寸（例如，毫米 `mm`）是根据之前计算出的目标像素尺寸、系统中配置的DPI（Dots Per Inch，默认为300）以及页边距（margin）计算得出的。
- `页面物理宽度 = (目标像素宽度 / DPI) * 25.4 + (页边距 * 2)`
- 随后，系统为每一张经过尺寸统一化处理的图片创建一个新的PDF页面，并将图片绘制在页面上。

## 总结

- **分辨率（DPI）**：在将图片数据写入PDF时，系统会参考配置的DPI值（如300 DPI）来决定图片的物理尺寸，这确保了打印时的清晰度。
- **页面尺寸**：PDF的页面尺寸是统一的，该尺寸是基于该批次所有图片尺寸的中位数动态计算出来的，旨在达到最佳的整体视觉效果和一致性。
- **图片保真度**：通过智能的调整策略（拉伸或填充），系统在统一页面尺寸的同时，也尽力保持了原始图片内容的比例和清晰度，避免了严重的失真。

这种处理方式的核心优势是能够生成专业、规整的多页PDF文档，即使用户上传的图片尺寸杂乱无章。
