import Flutter
import UIKit
import UniformTypeIdentifiers

// Use #if DEBUG directly around print statements for guaranteed optimization

@main
@objc class AppDelegate: FlutterAppDelegate {
  fileprivate var currentDelegate: DirectoryPickerDelegate?
  
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GeneratedPluginRegistrant.register(with: self)
    
    // Setup method channel after super call to ensure window is available
    let result = super.application(application, didFinishLaunchingWithOptions: launchOptions)
    
    setupMethodChannel()
    
    return result
  }
  
  private func setupMethodChannel() {
    guard let controller = window?.rootViewController as? FlutterViewController else {
      print("Error: Could not get FlutterViewController")
      return
    }
    
    let directoryPickerChannel = FlutterMethodChannel(
      name: "directory_picker",
      binaryMessenger: controller.binaryMessenger
    )
    
    directoryPickerChannel.setMethodCallHandler { [weak self] (call, result) in
      switch call.method {
      case "pickDirectory":
        self?.pickDirectory(result: result)
      case "getDirectoryFiles":
        self?.getDirectoryFiles(result: result)
      case "getDirectoryStats":
        self?.getDirectoryStats(call: call, result: result)
      case "getDirectoryFilesForPath":
        self?.getDirectoryFilesForPath(call: call, result: result)
      case "revokeAuthorization":
        self?.revokeAuthorization(call: call, result: result)
      case "clearAllBookmarks":
        self?.clearAllBookmarks(result: result)
      default:
        result(FlutterMethodNotImplemented)
      }
    }
  }
  
  private func revokeAuthorization(call: FlutterMethodCall, result: @escaping FlutterResult) {
    guard let args = call.arguments as? [String: Any], let path = args["path"] as? String else {
      result(FlutterError(code: "BAD_ARGS", message: "Missing 'path' argument", details: nil))
      return
    }
    
    let defaults = UserDefaults.standard
    var map = (defaults.dictionary(forKey: "savedFolderBookmarks") as? [String: Data]) ?? [:]
    
    if map.removeValue(forKey: path) != nil {
      defaults.set(map, forKey: "savedFolderBookmarks")
      #if DEBUG
      print("[DirectoryPicker] Revoked and removed bookmark for path: \(path)")
      #endif
    }
    
    // Also remove the legacy key just in case
    defaults.removeObject(forKey: "savedFolderBookmark")
    
    result(true)
  }
  
  private func clearAllBookmarks(result: @escaping FlutterResult) {
    #if DEBUG
    print("[DirectoryPicker] clearAllBookmarks called")
    #endif
    
    let defaults = UserDefaults.standard
    
    // Clear the main bookmarks dictionary
    if let bookmarks = defaults.dictionary(forKey: "savedFolderBookmarks") as? [String: Data] {
      #if DEBUG
      print("[DirectoryPicker] Clearing \(bookmarks.count) bookmarks")
      #endif
      defaults.removeObject(forKey: "savedFolderBookmarks")
    }
    
    // Also clear the legacy bookmark key
    defaults.removeObject(forKey: "savedFolderBookmark")
    
    #if DEBUG
    print("[DirectoryPicker] All bookmarks cleared")
    #endif
    
    result(true)
  }
  
  private func pickDirectory(result: @escaping FlutterResult) {
    guard let controller = window?.rootViewController else {
      result(FlutterError(code: "NO_CONTROLLER", message: "No view controller available", details: nil))
      return
    }
    
    // Create and retain delegate
    currentDelegate = DirectoryPickerDelegate(result: result, appDelegate: self)
    
    let picker = UIDocumentPickerViewController(forOpeningContentTypes: [UTType.folder])
    picker.delegate = currentDelegate
    picker.allowsMultipleSelection = false
    
    #if DEBUG
    print("[DirectoryPicker] About to present picker with delegate: \(String(describing: currentDelegate))")
    #endif
    controller.present(picker, animated: true, completion: nil)
  }
  
  private func getDirectoryFiles(result: @escaping FlutterResult) {
    #if DEBUG
    print("[DirectoryPicker] getDirectoryFiles called")
    #endif

    let defaults = UserDefaults.standard
    guard let bookmarks = defaults.dictionary(forKey: "savedFolderBookmarks") as? [String: Data], !bookmarks.isEmpty else {
      #if DEBUG
      print("[DirectoryPicker] No bookmarks dictionary found or it is empty")
      #endif
      // Return empty list if no bookmarks are present
      result([])
      return
    }

    #if DEBUG
    print("[DirectoryPicker] Found \(bookmarks.count) bookmarks")
    for (path, _) in bookmarks {
      print("[DirectoryPicker] Bookmark path: \(path)")
    }
    #endif

    var allFiles: [[String: Any]] = []
    var staleBookmarks: [String] = []

    for (path, bookmarkData) in bookmarks {
      do {
        var isStale = false
        let restoredURL = try URL(
          resolvingBookmarkData: bookmarkData,
          options: [],
          relativeTo: nil,
          bookmarkDataIsStale: &isStale
        )

        if isStale {
          #if DEBUG
          print("[DirectoryPicker] Bookmark for path \(path) is stale")
          #endif
          staleBookmarks.append(path)
          continue
        }

        let shouldStopAccessing = restoredURL.startAccessingSecurityScopedResource()
        defer { if shouldStopAccessing { restoredURL.stopAccessingSecurityScopedResource() } }

        let files = traverseDirectory(at: restoredURL)
        allFiles.append(contentsOf: files)

      } catch {
        #if DEBUG
        print("[DirectoryPicker] Failed to restore bookmark for path \(path): \(error)")
        #endif
        // Also treat resolution errors as stale
        staleBookmarks.append(path)
      }
    }

    // Clean up any stale bookmarks found during traversal
    if !staleBookmarks.isEmpty {
      var currentBookmarks = bookmarks
      for path in staleBookmarks {
        currentBookmarks.removeValue(forKey: path)
      }
      defaults.set(currentBookmarks, forKey: "savedFolderBookmarks")
      #if DEBUG
      print("[DirectoryPicker] Removed \(staleBookmarks.count) stale bookmarks")
      #endif
    }

    #if DEBUG
    print("[DirectoryPicker] Total files found across all directories: \(allFiles.count)")
    #endif
    result(allFiles)
  }

  // Return count and size for a specific directory path using stored bookmarks
  private func getDirectoryStats(call: FlutterMethodCall, result: @escaping FlutterResult) {
    #if DEBUG
    print("[DirectoryPicker] getDirectoryStats called")
    #endif
    guard let args = call.arguments as? [String: Any], let targetPath = args["path"] as? String else {
      result(FlutterError(code: "BAD_ARGS", message: "Missing 'path' argument", details: nil))
      return
    }

    // Load bookmarks map: [path: Data]
    let defaults = UserDefaults.standard
    var bookmarks: [String: Data] = [:]
    if let stored = defaults.dictionary(forKey: "savedFolderBookmarks") as? [String: Data] {
      bookmarks = stored
    } else if let legacy = defaults.data(forKey: "savedFolderBookmark") {
      // Legacy single bookmark: associate with its resolved path for best effort
      do {
        var isStale = false
        let url = try URL(resolvingBookmarkData: legacy, options: [], relativeTo: nil, bookmarkDataIsStale: &isStale)
        if !isStale { bookmarks[url.path] = legacy }
      } catch { /* ignore */ }
    }

    // Find the best (longest) bookmark whose path is a prefix of targetPath
    var bestKey: String?
    for key in bookmarks.keys {
      if targetPath == key || targetPath.hasPrefix(key.hasSuffix("/") ? key : key + "/") {
        if let currentBest = bestKey {
          if key.count > currentBest.count { bestKey = key }
        } else {
          bestKey = key
        }
      }
    }

    guard let bookmarkKey = bestKey, let bookmarkData = bookmarks[bookmarkKey] else {
      #if DEBUG
      print("[DirectoryPicker] No matching bookmark for path: \(targetPath)")
      #endif
      result(FlutterError(code: "NO_MATCHING_BOOKMARK", message: "No bookmark for path", details: targetPath))
      return
    }

    do {
      var isStale = false
      let rootURL = try URL(resolvingBookmarkData: bookmarkData, options: [], relativeTo: nil, bookmarkDataIsStale: &isStale)
      if isStale {
        result(FlutterError(code: "STALE_BOOKMARK", message: "Bookmark is stale", details: bookmarkKey))
        return
      }

      let shouldStop = rootURL.startAccessingSecurityScopedResource()
      defer { if shouldStop { rootURL.stopAccessingSecurityScopedResource() } }

      // Derive the target URL under the root bookmark
      let targetURL: URL
      if targetPath == bookmarkKey {
        targetURL = rootURL
      } else {
        let startIndex = targetPath.index(targetPath.startIndex, offsetBy: bookmarkKey.count)
        var rel = String(targetPath[startIndex...])
        while rel.hasPrefix("/") { rel.removeFirst() }
        targetURL = rootURL.appendingPathComponent(rel)
      }

      var fileCount = 0
      var totalBytes: Int64 = 0
      let fm = FileManager.default
      if let enumerator = fm.enumerator(at: targetURL, includingPropertiesForKeys: [.isDirectoryKey, .fileSizeKey], options: [.skipsHiddenFiles]) {
        for case let fileURL as URL in enumerator {
          do {
            let values = try fileURL.resourceValues(forKeys: [.isDirectoryKey, .fileSizeKey])
            if values.isDirectory == false {
              fileCount += 1
              totalBytes += Int64(values.fileSize ?? 0)
            }
          } catch {
            #if DEBUG
            print("[DirectoryPicker] Error reading \(fileURL.path): \(error)")
            #endif
          }
        }
      } else {
        #if DEBUG
        print("[DirectoryPicker] Could not enumerate target URL: \(targetURL.path)")
        #endif
      }

      result(["fileCount": fileCount, "totalBytes": totalBytes])
    } catch {
      result(FlutterError(code: "BOOKMARK_ERROR", message: "Failed to resolve bookmark: \(error)", details: nil))
    }
  }
  
  private func traverseDirectory(at url: URL) -> [[String: Any]] {
    #if DEBUG
    print("[DirectoryPicker] Traversing directory: \(url.path)")
    #endif
    let fileManager = FileManager.default
    var files: [[String: Any]] = []
    
    guard let enumerator = fileManager.enumerator(
      at: url,
      includingPropertiesForKeys: [.nameKey, .isDirectoryKey, .fileSizeKey, .contentModificationDateKey],
      options: [.skipsHiddenFiles]
    ) else {
      #if DEBUG
    print("[DirectoryPicker] Failed to create file enumerator")
    #endif
      return files
    }
    
    var processedCount = 0
    for case let fileURL as URL in enumerator {
      processedCount += 1
      do {
        let resourceValues = try fileURL.resourceValues(forKeys: [
          .nameKey, .isDirectoryKey, .fileSizeKey, .contentModificationDateKey
        ])
        
        let isDirectory = resourceValues.isDirectory ?? false
        let name = resourceValues.name ?? ""
        let fileSize = resourceValues.fileSize ?? 0
        let modificationDate = resourceValues.contentModificationDate
        
        #if DEBUG
    print("[DirectoryPicker] Processing: \(name), isDirectory: \(isDirectory)")
    #endif
        
        if !isDirectory {
          var fileInfo: [String: Any] = [
            "name": name,
            "path": fileURL.path,
            "size": fileSize,
            "isDirectory": false
          ]
          
          if let date = modificationDate {
            fileInfo["modificationDate"] = Int64(date.timeIntervalSince1970 * 1000)
          }
          
          files.append(fileInfo)
          #if DEBUG
    print("[DirectoryPicker] Added file: \(name)")
    #endif
        }
      } catch {
        #if DEBUG
    print("[DirectoryPicker] Error getting file attributes for \(fileURL.path): \(error)")
    #endif
      }
    }
    
    #if DEBUG
    print("[DirectoryPicker] Processed \(processedCount) items, found \(files.count) files")
    #endif
    return files
  }

  // Enumerate files for a specific target path using stored bookmarks
  private func getDirectoryFilesForPath(call: FlutterMethodCall, result: @escaping FlutterResult) {
    #if DEBUG
    print("[DirectoryPicker] getDirectoryFilesForPath called")
    #endif
    guard let args = call.arguments as? [String: Any], let targetPath = args["path"] as? String else {
      result(FlutterError(code: "BAD_ARGS", message: "Missing 'path' argument", details: nil))
      return
    }

    let defaults = UserDefaults.standard
    var bookmarks: [String: Data] = [:]
    if let stored = defaults.dictionary(forKey: "savedFolderBookmarks") as? [String: Data] {
      bookmarks = stored
    } else if let legacy = defaults.data(forKey: "savedFolderBookmark") {
      // Recover legacy bookmark
      do {
        var isStale = false
        let url = try URL(resolvingBookmarkData: legacy, options: [], relativeTo: nil, bookmarkDataIsStale: &isStale)
        if !isStale { bookmarks[url.path] = legacy }
      } catch { /* ignore */ }
    }

    // Pick longest prefix bookmark
    var bestKey: String?
    for key in bookmarks.keys {
      if targetPath == key || targetPath.hasPrefix(key.hasSuffix("/") ? key : key + "/") {
        if let currentBest = bestKey {
          if key.count > currentBest.count { bestKey = key }
        } else {
          bestKey = key
        }
      }
    }

    guard let bookmarkKey = bestKey, let bookmarkData = bookmarks[bookmarkKey] else {
      result(FlutterError(code: "NO_MATCHING_BOOKMARK", message: "No bookmark for path", details: targetPath))
      return
    }

    do {
      var isStale = false
      let rootURL = try URL(resolvingBookmarkData: bookmarkData, options: [], relativeTo: nil, bookmarkDataIsStale: &isStale)
      if isStale {
        result(FlutterError(code: "STALE_BOOKMARK", message: "Bookmark is stale", details: bookmarkKey))
        return
      }

      let shouldStop = rootURL.startAccessingSecurityScopedResource()
      defer { if shouldStop { rootURL.stopAccessingSecurityScopedResource() } }

      let targetURL: URL
      if targetPath == bookmarkKey {
        targetURL = rootURL
      } else {
        let startIndex = targetPath.index(targetPath.startIndex, offsetBy: bookmarkKey.count)
        var rel = String(targetPath[startIndex...])
        while rel.hasPrefix("/") { rel.removeFirst() }
        targetURL = rootURL.appendingPathComponent(rel)
      }

      let files = traverseDirectory(at: targetURL)
      result(files)
    } catch {
      result(FlutterError(code: "BOOKMARK_ERROR", message: "Failed to resolve bookmark: \(error)", details: nil))
    }
  }
}

class DirectoryPickerDelegate: NSObject, UIDocumentPickerDelegate {
  private let result: FlutterResult
  weak var appDelegate: AppDelegate?
  
  init(result: @escaping FlutterResult, appDelegate: AppDelegate? = nil) {
    self.result = result
    self.appDelegate = appDelegate
  }
  
  func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
    #if DEBUG
    print("[DirectoryPicker] Directory picker callback called with \(urls.count) URLs")
    #endif
    
    guard let url = urls.first else {
      #if DEBUG
    print("[DirectoryPicker] No URL selected")
    #endif
      result(FlutterError(code: "NO_DIRECTORY", message: "No directory selected", details: nil))
      return
    }
    
    #if DEBUG
    print("[DirectoryPicker] Selected directory: \(url.path)")
    #endif
    
    // Start accessing security-scoped resource before creating bookmark
    let shouldStopAccessing = url.startAccessingSecurityScopedResource()
    #if DEBUG
    print("[DirectoryPicker] Started security-scoped access: \(shouldStopAccessing)")
    #endif
    
    defer {
      if shouldStopAccessing {
        url.stopAccessingSecurityScopedResource()
        #if DEBUG
    print("[DirectoryPicker] Stopped security-scoped access")
    #endif
      }
    }
    
    do {
      // Create bookmark data for persistent access (iOS style - no withSecurityScope option)
      let bookmarkData = try url.bookmarkData(
        options: [],
        includingResourceValuesForKeys: nil,
        relativeTo: nil
      )
      
      #if DEBUG
    print("[DirectoryPicker] Created bookmark data of size: \(bookmarkData.count)")
    #endif
      
      // Save bookmark data for future access (support multiple paths)
      let defaults = UserDefaults.standard
      var map = (defaults.dictionary(forKey: "savedFolderBookmarks") as? [String: Data]) ?? [:]
      map[url.path] = bookmarkData
      defaults.set(map, forKey: "savedFolderBookmarks")
      // Also keep legacy single key for backward compatibility
      defaults.set(bookmarkData, forKey: "savedFolderBookmark")
      #if DEBUG
      print("[DirectoryPicker] Saved bookmark for path: \(url.path)")
      #endif
      
      result([
        "path": url.path,
        "name": url.lastPathComponent,
        "success": true
      ])
      
      #if DEBUG
    print("[DirectoryPicker] Returning success result")
    #endif
      
    } catch {
      #if DEBUG
    print("[DirectoryPicker] Failed to create bookmark: \(error)")
    #endif
      result(FlutterError(code: "BOOKMARK_ERROR", message: "Failed to create bookmark: \(error)", details: nil))
    }
    
    // Clear the delegate reference
    appDelegate?.currentDelegate = nil
  }
  
  func documentPickerWasCancelled(_ controller: UIDocumentPickerViewController) {
    #if DEBUG
    print("[DirectoryPicker] Directory picker was cancelled")
    #endif
    result(FlutterError(code: "CANCELLED", message: "Directory selection was cancelled", details: nil))
    // Clear the delegate reference
    appDelegate?.currentDelegate = nil
  }
}
